events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    sendfile        on;
    keepalive_timeout  65;
    
    # WordPress Site
    server {
        listen 80;
        server_name localhost;
        root /var/www/html;
        index index.php index.html index.htm;
        
        # WordPress pretty permalinks
        location / {
            try_files $uri $uri/ /index.php?$args;
        }
        
        # PHP-FPM configuration
        location ~ \.php$ {
            fastcgi_pass wordpress:9000;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
        }
        
        # Static files
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Deny access to sensitive files
        location ~ /\. {
            deny all;
        }
        
        location ~ /wp-config.php {
            deny all;
        }
    }
    
    # IDX Server Proxy
    server {
        listen 80;
        server_name idx.test idx-server.test;
        
        location / {
            proxy_pass http://newidx:8155;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
    
    # Solr Proxy
    server {
        listen 80;
        server_name solr.test;
        
        location / {
            proxy_pass http://solr:8983;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
}
