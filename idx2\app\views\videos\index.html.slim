h2 Videos

/ TODO: I stopped with this section when I realized I need business domain understanding.
/ Elsewhere, when a video is added, we add the video to all clients tied to this user.
/ However, it seems a global video might not make sense the same way. I'll have to ask.
/ .global-video
  / h2 Agent Global Video
  # = simple_form_for @client do |f|
    # = f.input :access_youtube_id
    # = f.button :submit, value: "Set global video"

.new-video
  h3 Add video
  = simple_form_for @video do |f|
    = f.input :MLS_id, label: "MLS #"
    = f.input :youtube_id, label: "YouTube ID"
    = f.button :submit, value: "Add video", class: 'btn-primary'
.row
  .span12
    h3 Videos for listings 
    - if @videos.count == 0
      b You haven't added any videos yet
    - else
      table.table.table-striped.table-bordered.auto-width.videos
        tr
          th MLS #
          th YouTube ID
          th info
          th delete
        - for video in @videos
          tr class="video video_#{video.id}" data-mls-id="#{video.MLS_id}" data-youtube-id="#{video.youtube_id}"
            td = video.MLS_id
            td = video.youtube_id
            td.info
              = link_to "<i class='icon-info-sign icon-white'></i> Show more info".html_safe, "#", class: "btn btn-info btn-small show-info"
              .listing_placeholder
              .video_placeholder
            td = link_to "<i class='icon-remove icon-white'></i> Delete".html_safe, video_path(video), method: :delete, class: "btn btn-danger btn-small", data: { confirm: "Are you sure you want to delete this video?" }


