require File.expand_path('../boot', __FILE__)

require 'rails/all'

# Assets should be precompiled for production (so we don't need the gems loaded then)
Bundler.require(:default, Rails.env)

module Idx2
  class Application < Rails::Application
    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration should go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded.

    # Set Time.zone default to the specified zone and make Active Record auto-convert to this zone.
    # Run "rake -D time" for a list of tasks for finding time zone names. Default is UTC.
    # config.time_zone = 'Central Time (US & Canada)'

    # The default locale is :en and all translations from config/locales/*.rb,yml are auto loaded.
    # config.i18n.load_path += Dir[Rails.root.join('my', 'locales', '*.{rb,yml}').to_s]
    # config.i18n.default_locale = :de

    # Set up autoloading for lib dir.
    # From: http://stackoverflow.com/a/19650564/135101
    config.autoload_paths << Rails.root.join('lib')
    # Explicitly require files that don't follow autoloading naming conventions.
    paths_to_require = %w{utils}
    paths_to_require.each do |path|
      require "#{Rails.root}/app/lib/#{path}.rb"
    end

    config.from_file 'profound.yml'
  end
end
