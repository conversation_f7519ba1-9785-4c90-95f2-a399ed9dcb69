= simple_form_for [@reseller, @reseller_pricing] do |f|
  = f.error_notification

  div.form-inputs
    = f.input :promo_code, hint: "Promo codes should be unique across all resellers"
    = f.input :theme, hint: "This must exactly match the theme as shown in our system code. Here is a list: " + theme_list
    = f.input :recurring_method_id, hint: "This is set up on the eProcessing Network site and determines the order total, overriding the price you set here!"
    = f.input :monthly_rate, hint: "Reminder that this rate is meant as a reminder here only. It will actually be overridden by the amount set on the ePN recurring method, set on their admin site."
    = f.input :payment_url, hint: "The URL where we'll send people to pay for their site (for this promo code / package)"

  div.form-actions
    = f.button :submit
