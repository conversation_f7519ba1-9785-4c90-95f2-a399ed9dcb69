= render 'intro'

/ Increase the width for the select boxes that had text cutoff
<style>.step select { width: 300px; }</style>

.label.label-info Head's up!
strong< Please look over the form before starting it, to know what information you need to have ready.

.new_client_registration_form
  = simple_form_for @client, url: @form_action_url || clients_path, html: { class: 'form-horizontal' } do |f|
    = f.button :submit, "Submit (shown in development mode only)" if Rails.env.development?
    = f.error_notification
    / Reminder: I was trying to dump out top level errors here.
    / I couldn't find a good way, because all of the errors are dumped
    / into "base", including "Users isn't valid", which really means
    / the users association is invalid, but that's zero help to the user.
    / So for now I'm taking it out, hoping I'll find a better way later,
    / but note this means only showing the client errors
    / those stemming from model.save calls (thus any other exception
    / would not be shown and the user would be clueless how to proceed).
    / - if @client.errors[:base].any?
    /   .alert.alert-error
    /     = @client.errors[:base]
    / UPDATE: My new idea is to show a collapsible element which will
    / show all errors when expanded.
    - objects = [@client]
    - objects << @registration if @registration
    - if objects.any? { |o| o.errors.count > 0 }
      .show_errors
        | Most errors should be shown below in red.
        button.btn.btn-mini<>(type="button" data-toggle="collapse" data-target=".full_errors") Click here
        | to show all errors here.
      .full_errors.collapse
        - objects.each do |object|
          ul
            - object.errors.full_messages.each do |msg|
              li= msg

    - if @is_main_mu
      .step
        h3 I am signing up for:
        .form-inputs
          <div class="control-group">
          <label class="control-label" for="cat_site_marketing">Marketing Platform Only</label>
          <div class="controls">
          <input id="cat_site_marketing" name="helper[cat_site]" value="marketing" type="radio">
          </div></div>
          <div class="control-group">
          <label class="control-label" for="cat_site_basic">Premium Site + Marketing Platform</label>
          <div class="controls">
          <input checked="checked" id="cat_site_basic" name="helper[cat_site]" value="basic" type="radio">
          </div></div>
    - if @is_westusa
      .step
          / TODO: Leaving this as a radio button, but hiding with CSS, because the JavaScript is hard-coded to only look for a radio input.  :-(
          <input type="radio" id="cat_site_free" name="helper[cat_site]" value="cat_site_free" checked="checked" style="display: none">
          = f.hidden_field :feature_gui, value: "1"
      .step
        h3 Domain Information
        .form-inputs
          = f.fields_for @registration do |reg_form|
            = reg_form.input :existing_website_url, label: "Please enter a domain name: ", placeholder: "http://", hint: "If you don't have a domain name please purchase one here: ".html_safe + link_to('http://domainwithresults.com/', 'http://domainwithresults.com/', target: '_blank') + " and enter it above."

    .step
      h3 Contact Information
      .form-inputs
        = f.input :access_fullname, required: true
        / FIXME We will should allow a better way of customization on this form
        / but we needed this ASAP
        - if !@is_westusa
          = f.input :access_address
          = f.input :access_address2
          = f.input :access_city
          = f.input :access_state, collection: Carmen::Country.named('United States').subregions, prompt: "<Please select a state>", value_method: lambda { |state| state.code }
          = f.input :access_zipcode
        = f.input :access_phone
        = f.input :access_emailaddress, required: true
        - if !@is_westusa
          = f.input :access_company
        / TODO We should do that for every reseller
        - if @is_westusa
          = f.hidden_field :mls, value: "armls"
        - else
          = f.input :mls, collection: MlsSystem.all, label_method: lambda { |m| m.display_name }, value_method: lambda { |m| m.name }, prompt: '<Please select your MLS>', error: 'must select'
        = f.input :access_member_id, required: true
        - if !@is_westusa
          = f.input :access_office_id
          = f.input :broker_office_name
        = f.hidden_field :reseller_id, value: current_reseller.try(:id)
        / Reminder: the above reseller id is put on the client record. The following is for the form, so that if there are any errors, when the form is shown again, it's still in reseller mode.
        = hidden_field_tag "reseller_friendlyid", current_reseller.try(:friendlyid)
    - if @registration
      .step
        h3 Website Information
        .form-inputs
          = f.fields_for @registration do |reg_form|
            - if @is_westusa
              / FIXME: Hardcoding the options here as I don't want to spend extra time maintaing or hacking this.  This system/form needs to be replaced completely.
              <div class="control-group select required client_registration_theme">
                <label class="select required control-label" for="client_registration_theme">
                  <abbr title="required">*</abbr> Region / MLS
                </label>
                <div class="controls">
                  <select class="select required" id="client_registration_theme" name="client[registration][theme]">
                    <option value="" selected>&lt;Please select your region / MLS&gt;</option>
                    <option value="Payson (CABOR)">Payson (CABOR)</option>
                    <option value="Phoenix (ARMLS)">Phoenix (ARMLS)</option>
                    <option value="White Mountains (WMAR)">White Mountains (WMAR)</option>
                  </select>
                </div>
              </div>
              /= reg_form.input :theme, collection: get_themes, prompt: '<Please select your region / MLS>'
            - else
              = reg_form.input :theme, collection: get_themes, prompt: '<Please select your theme>', hint: link_to("See theme previews", theme_previews_url, target: "_new")
            = reg_form.input :color_scheme, collection: [], prompt: "<Please select your color scheme>"
            / This hidden input allows us to reselect the user's choice in the event of an error
            / with the user's values and we have to redisplay the form.
            = reg_form.input :color_scheme, as: :hidden
            - if !@is_westusa
              = reg_form.input :existing_website_url, label: "Do you have an existing website? If so, enter its address here.", placeholder: "http://"
            = reg_form.input :facebook_url, placeholder: "http://"
            = reg_form.input :linkedin_url, placeholder: "http://"
            = reg_form.input :twitter_url, placeholder: "http://"
            = reg_form.input :google_plus_url, placeholder: "http://"
            = reg_form.input :youtube_url, placeholder: "http://"
            = reg_form.input :other_social_media
            - if @registration.logo?
              .control-group
                label Logo preview
                .controls
                  = image_tag @registration.logo_url
            - else
              - if @is_westusa
                = reg_form.input :logo, hint: "Personalized/Team logo to be included in the website banner. This is ideally a transparent PNG."
              - else
                = reg_form.input :logo, hint: "Your company logo to be included in the website banner. This is ideally a transparent PNG."
            = reg_form.input :logo_cache, as: "hidden"
            - if @registration.headshot?
              .control-group
                label.control-label Headshot preview
                .controls
                  = image_tag @registration.headshot_url
            - else
              = reg_form.input :headshot, hint: "A small headshot image for the top of your website. Please submit a JPG."
            = reg_form.input :headshot_cache, as: "hidden"
            - if !@is_westusa
              = reg_form.input :market_area, label: "Please define your home page communities", hint: "Example: Chandler Short Sales, Chandler Homes between $100,000 – $250,000, Park Promenade Homes for Sale Chandler, Chandler Homes with Pools, etc."
              = reg_form.input :title, hint: "This is what shows in the browser's title bar"
            = reg_form.input :tagline, hint: 'Ex: "For All Your Real Estate Needs"'
            = reg_form.input :notes, label: "Other notes?", hint: "E.g. other phone numbers you want listed on the site?"
      / Case 1531, don't show payment form.
      / render 'payment_info', f: f

      - if !@is_westusa
        br
        .label.label-info Almost done!
        strong< When we contact you, we'll want to know more about your business, so please have the following info ready.
        br
        br
        br

        ol
          = render 'sliders'
          li You may have up to 10 pages of content that you would like to put in your new site. If you need additional pages let us know and we&#8217;ll work something out. If you have a current website, we can transfer your current website&#8217;s content to your new site with us. We&#8217;ll transfer the first 10 pages as part of setting up your new website. If you have more than that, we can work something out to get all the content you want into your new website.
          li We'll need your domain registrar logins and passwords.  {ex. Godaddy} We will need this to change your DNS (where your domain name points to have your website show up). If you know how to do this, let us know and we&#8217;ll give you the name servers to use in your registrar account.
          = render 'kickoff'

    .form-actions
      = f.button :submit, "Submit my #{current_reseller.try(:display_name) || 'iFoundAgent' } registration!", class: "btn btn-primary btn-large pfmls-get-started-form-submit-button"
