class MakePhoneNumbersE164 < ActiveRecord::Migration
  def change
    # Update: after thinking about it, this was a dumb thing to do for a one-time fix. I should've just run it manually.
    # It doesn't make sense as a migration.
    # Remove non digits
#     execute <<-SQL
#       update sms_messages set `to` = concat('+1', replace(replace(replace(replace(replace(`to`, ' ' ,''), '(', ''), ')', ''), '+', ''), '-', ''));
#     SQL
  end
end
