version: '3.8'

services:
  # Database for IDX
  idx_db:
    image: mysql:5.7.44
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: idx_development
    ports:
      - "33066:3306"
    volumes:
      - ../volumes/idx_db:/var/lib/mysql

  # Database for WordPress
  wpdb:
    image: mysql:5.7.44
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: wordpress
      MYSQL_DATABASE: wordpress
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
    ports:
      - "33067:3306"
    volumes:
      - ../volumes/wpdb:/var/lib/mysql

  # Solr Search Engine
  solr:
    image: solr:8.11.2
    restart: unless-stopped
    ports:
      - "8983:8983"
    volumes:
      - ../volumes/solr:/var/solr
    command: ["solr-foreground"]

  # Nginx Web Server
  nginx:
    image: nginx:1.13.0-alpine
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx-simple.conf:/etc/nginx/nginx.conf
      - ../volumes/wordpress:/var/www/html
      - ../plugin:/var/www/html/wp-content/plugins/profoundmls
    depends_on:
      - wordpress
      - solr

  # Node.js IDX Server (simplified)
  newidx:
    image: node:18-bullseye-slim
    restart: unless-stopped
    working_dir: /www
    ports:
      - "8155:8155"
    volumes:
      - ../server:/www
    environment:
      - NODE_ENV=development
    command: ["tail", "-f", "/dev/null"]
    depends_on:
      - idx_db
      - solr

  # WordPress with PHP-FPM
  wordpress:
    image: wordpress:6.3-php8.1-fpm
    restart: unless-stopped
    environment:
      WORDPRESS_DB_HOST: wpdb
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress
      WORDPRESS_DB_NAME: wordpress
    volumes:
      - ../volumes/wordpress:/var/www/html
      - ../plugin:/var/www/html/wp-content/plugins/profoundmls
      - ../ifound:/var/www/html/wp-content/plugins/ifound
      - ../ifound-themes:/var/www/html/wp-content/themes/ifound-themes
    depends_on:
      - wpdb
