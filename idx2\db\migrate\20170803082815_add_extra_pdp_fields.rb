class AddExtraPdpFields < ActiveRecord::Migration
  def change
    add_column :mls_systems, :meta_pdp_main_label, :string, :default => 'PDP Main Content'
    add_column :mls_systems, :meta_pdp_main_value, :text 
    add_column :mls_systems, :meta_pdp_features_label, :string, :default => 'Property Features'
    add_column :mls_systems, :meta_pdp_features_value, :text 
    add_column :access_meta, :meta_pdp_main_label, :string, :default => 'PDP Main Content'
    add_column :access_meta, :meta_pdp_main_value, :text 
    add_column :access_meta, :meta_pdp_features_label, :string, :default => 'Property Features'
    add_column :access_meta, :meta_pdp_features_value, :text 
  end
end
