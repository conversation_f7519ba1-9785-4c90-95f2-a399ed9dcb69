class RegistrationMailer < ActionMailer::Base
  helper :mailer
  helper :client

  helper_method :current_reseller
  helper_method :has_attachment?
  helper_method :attachment_url
  helper_method :current_theme

  default from: "iFoundAgent <<EMAIL>>"
  layout "email"
  default css: ["email"]

  # I would prefer to make this an attr_acessor and then initialize it in initialize,
  # but the mailer previews somehow call the method (like 'welcome') and THEN
  # initialize. I'm not even sure how that's possible.
  def attachment_info
    @attachment_info ||= {
      logo: { is_attached: false, url: nil },
      headshot: { is_attached: false, url: nil },
    }
  end

  def prepare_reseller(client)
    if client.reseller || client.reseller_id
      reseller = client.reseller || Reseller.find(client.reseller_id)
      prepend_view_path ["app/views/whitelabel/#{reseller.friendlyid}", "app/views/whitelabel"]
      @reseller = reseller
    end
  end

  def current_reseller
    @reseller
  end

  def current_theme
    @registration.try(:theme)
  end

  def welcome(user, password, client, registration)
    prepare_reseller client
    @user = user
    @password = password
    @client = client
    @registration = registration
    @payment_url = ResellerPricing.compute_payment_url reseller_id: client.reseller_id, promo_code: registration.promo_code

    @preview_text = "Here is your registration information from iFoundAgent"
    self.add_header_logo
    self.add_logo_and_headshot

    mail to: "#{@client.access_fullname} <#{@user.email}>", subject: "iFoundAgent registration: welcome!", css: ["email", "registration_mailer"]
  end

  def welcome_idx_access_only(client)
    prepare_reseller client
    @client = client
    @payment_url = ResellerPricing.compute_payment_url reseller_id: client.reseller_id, reseller: client.reseller

    @preview_text = "Here is your registration information from iFoundAgent"

    mail to: "#{@client.access_fullname} <#{@client.access_emailaddress}>", subject: "iFoundAgent registration: welcome!", css: ["email", "registration_mailer"]
  end

  def notify_admin(client, registration, email_address)
    @client = client
    @registration = registration

    @preview_text = "A new client registered for the iFoundAgent"
    self.add_header_logo
    self.add_logo_and_headshot

    mail to: email_address, subject: "iFoundAgent new client registration", css: ["email", "registration_mailer"]
  end

  def notify_reseller_idx_access_only(client, email_address)
    return nil unless client.reseller
    @client = client

    @preview_text = "A new client registered for iFoundAgent service"

    mail to: email_address, subject: "iFoundAgent new client registration", css: ["email", "registration_mailer"]
  end

  def site_build(registration)
    @registration = registration
    @client = registration.client

    @preview_text = "The website build process has finished"
    self.add_header_logo

    mail to: Rails.configuration.admin_email_addresses, subject: "iFoundAgent.com website build finished", css: ["email", "registration_mailer"]
  end

  def add_header_logo
    # Attachments are currently broken in mail previews.
    # See https://github.com/rails/rails/pull/14519
    # For now, don't attach them in development so we can use the previews.
    # We're still able to see image placeholders, which is good enough.
    if !Rails.env.development?
      attachments.inline['email_header.png'] = File.read(File.join(Rails.root, 'app/assets/images/ifoundagent_email_header.png'))
    end
  end

  def add_logo_and_headshot
    # Attachments are currently broken in mail previews.
    # See https://github.com/rails/rails/pull/14519
    # For now, don't attach them in development so we can use the previews.
    # We're still able to see image placeholders, which is good enough.
    if !Rails.env.development?
      # TODO: If the uploaded files had the same filename but were different
      # images, this would overwrite the first.
      if @registration.logo?
        filename = File.basename(@registration.logo.current_path)
        attachments.inline[filename] = File.read(@registration.logo.current_path)
        self.attachment_info[:logo][:is_attached] = true
        self.attachment_info[:logo][:url] = attachments.inline[filename].url
      end
      if @registration.headshot?
        filename = File.basename(@registration.headshot.current_path)
        attachments.inline[filename] = File.read(@registration.headshot.current_path)
        self.attachment_info[:headshot][:is_attached] = true
        self.attachment_info[:headshot][:url] = attachments.inline[filename].url
      end
    end
  end

  # 'name' here means :logo or :headshot.
  def has_attachment?(name)
    self.attachment_info[name][:is_attached]
  end

  def attachment_url(name)
    self.attachment_info[name][:url]
  end
end
