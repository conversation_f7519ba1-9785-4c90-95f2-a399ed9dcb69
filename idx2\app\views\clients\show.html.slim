.client_show_page

  h1= @client.title

  a(href=clients_path)
    | &laquo; Return to clients list

  p= link_to "Edit".html_safe, edit_client_path(@client), class: "btn btn-primary btn-large edit-button-padding"

  .contact_information
    h3 Contact information
    .name-value-pairs
      dl
        dt.form_label Client ID
        dd.form_label= nbsp(@client.id)
      dl
        dt.form_label Full name
        dd.form_label= nbsp(@client.access_fullname)
      dl
        dt.form_label Company name
        dd.form_label= nbsp(@client.access_company)
      dl
        dt.form_label Address line 1
        dd.form_label= nbsp(@client.access_address)
      dl
        dt.form_label Address line 2
        dd.form_label= nbsp(@client.access_address2)
      dl
        dt.form_label City
        dd.form_label= nbsp(@client.access_city)
      dl
        dt.form_label State
        dd.form_label= nbsp(@client.access_state)
      dl
        dt.form_label Zip code
        dd.form_label= nbsp(@client.access_zipcode)
      dl
        dt.form_label Phone
        dd.form_label= nbsp(@client.access_phone)
      dl
        dt.form_label Email
        dd.form_label= nbsp(@client.access_emailaddress)
    .account_information
      h3 Account information
      .name-value-pairs
        dl
          dt.form_label Status
          dd.form_label= nbsp(@client.access_status)
        dl
          dt.form_label Account ID
          dd.form_label= nbsp(@client.access_account_id)
        dl
          dt.form_label API Key
          dd.form_label= nbsp(@client.access_apikey)
        dl
          dt.form_label MLS
          dd.form_label= nbsp(@client.mls)
        dl
          dt.form_label MLS member ID
          dd.form_label= nbsp(@client.access_member_id)
        dl
          dt.form_label MLS office ID
          dd.form_label= nbsp(@client.access_office_id)
        dl
          dt.form_label Broker office name
          dd.form_label= nbsp(@client.broker_office_name)
        dl
          dt.form_label Youtube ID
          dd.form_label= nbsp(@client.access_youtube_id)
        dl
          dt.form_label Hide UCB
          dd.form_label= nbsp(@client.hide_ucb.humanize)
        dl
          dt.form_label Domain
          dd.form_label= nbsp(@client.domain)
        dl
          dt.form_label FTP Address
          dd.form_label= nbsp(@client.ftp_addr)
        dl
          dt.form_label FTP User
          dd.form_label= nbsp(@client.ftp_user)
        dl
          dt.form_label FTP Password
          dd.form_label= nbsp(@client.ftp_pass)
        dl
          dt.form_label URLs
          dd.form_label= nbsp(@client.URLs)
        dl
          dt.form_label Reseller
          dd.form_label= nbsp(@client.reseller.try(:display_name))

  .registration
    h3 Website information (from registration form, if available)
    - if @registration
      p = link_to "Registration admin", client_registration_path(@client), class: "btn btn-info"
      .name-value-pairs
        dl
          dt.form_label Theme
          dd.form_label= nbsp(@registration.theme)
        dl
          dt.form_label Color scheme
          dd.form_label= nbsp(@registration.color_scheme)
        dl
          dt.form_label Existing website
          dd.form_label= nbsp(@registration.existing_website_url)
        dl
          dt.form_label Facebook URL
          dd.form_label= nbsp(@registration.facebook_url)
        dl
          dt.form_label LinkedIn URL
          dd.form_label= nbsp(@registration.linkedin_url)
        dl
          dt.form_label Twitter URL
          dd.form_label= nbsp(@registration.twitter_url)
        dl
          dt.form_label Google Plus URL
          dd.form_label= nbsp(@registration.google_plus_url)
        dl
          dt.form_label YouTube URL
          dd.form_label= nbsp(@registration.youtube_url)
        dl
          dt.form_label Other social media
          dd.form_label= nbsp(@registration.other_social_media)
        dl
          dt.form_label Logo image
          dd.form_label= @registration.logo? ? image_tag(@registration.logo.url) : "[None entered]"
        dl
          dt.form_label Headshot image
          dd.form_label= @registration.headshot? ? image_tag(@registration.headshot.url) : "[None entered]"
        dl
          dt.form_label Market area
          dd.form_label= nbsp(@registration.market_area)
        dl
          dt.form_label Title
          dd.form_label= nbsp(@registration.title)
        dl
          dt.form_label Tagline
          dd.form_label= nbsp(@registration.tagline)
        dl
          dt.form_label Promo code
          dd.form_label= nbsp(@registration.promo_code)
        dl
          dt.form_label Other notes
          dd.form_label= nbsp(@registration.notes)
        dl
          dt.form_label ePN response
          dd.form_label= nbsp(@registration.epn_response)
        dl
          dt.form_label Website state
          dd.form_label= nbsp(@registration.website_state)
        dl
          dt.form_label Website request
          dd.form_label= nbsp(@registration.website_request)
        dl
          dt.form_label Website response
          dd.form_label= nbsp(@registration.website_response)
        dl
          dt.form_label Payment state
          dd.form_label= nbsp(@registration.payment_state)
    - else
      .muted= "[No registration]"

  .access_meta
    h3 SEO data
    .alert.alert-warning Reminder: these don't matter any more. Only the templates from the MLS system are used.
    .category
      h4 Category
      .name-value-pairs
        dl
          dt.form_label Page Title
          dd.form_label= nbsp(@access_meta.meta_cat_title)
        dl
          dt.form_label Page H1
          dd.form_label= nbsp(@access_meta.meta_cat_h1)
        dl
          dt.form_label Description META
          dd.form_label= nbsp(@access_meta.meta_cat_description)
        dl
          dt.form_label Keywords META
          dd.form_label= nbsp(@access_meta.meta_cat_keywords)
    .search_results
      h4 Results
      .name-value-pairs
        dl
          dt.form_label Page Title
          dd.form_label= nbsp(@access_meta.meta_result_title)
        dl
          dt.form_label Page H1
          dd.form_label= nbsp(@access_meta.meta_result_h1)
        dl
          dt.form_label Description META
          dd.form_label= nbsp(@access_meta.meta_result_description)
        dl
          dt.form_label Property H2
          dd.form_label= nbsp(@access_meta.meta_result_prop_h2)
        dl
          dt.form_label Property Content
          dd.form_label= nbsp(@access_meta.meta_result_prop_content)
        dl
          dt.form_label Keywords META
          dd.form_label= nbsp(@access_meta.meta_result_keywords)
    .property
      h4 Property
      .name-value-pairs
        dl
          dt.form_label URL
          dd.form_label= nbsp(@access_meta.meta_prop_url)
        dl
          dt.form_label Page Title
          dd.form_label= nbsp(@access_meta.meta_prop_title)
        dl
          dt.form_label Page H1
          dd.form_label= nbsp(@access_meta.meta_prop_h1)
        dl
          dt.form_label Description META
          dd.form_label= nbsp(@access_meta.meta_prop_description)
        dl
          dt.form_label Keywords META
          dd.form_label= nbsp(@access_meta.meta_prop_keywords)
    .clearfix

    h3 Templates
    .alert.alert-warning Reminder: these don't matter any more. Only the templates from the MLS system are used.
    .templates
      .clearfix
  .name-value-pairs
    dl
      dt.form_label Disclosure
      dd.form_label
        = render 'shared/disclosure_reminder'
        div= @access_meta.meta_disclosure
	  dl
	    dt.form_label Address
	    dd.form_label= @access_meta.meta_address
	  dl
	    dt.form_label Street Address
	    dd.form_label= @access_meta.meta_street_address
      .clearfix
      h4 Property Display Page
      .property-display-page
	h5 PDP Main Content
	.property_description
	  .name-value-pairs
	    dl
	      dt.form_label Label
	      dd.form_label= @access_meta.meta_pdp_main_label
	    dl
	      dt.form_label Value
	      dd.form_label= @access_meta.meta_pdp_main_value
	.clearfix
	h5 Property Description
	.property_description
	  .name-value-pairs
	    dl
	      dt.form_label Label
	      dd.form_label= @access_meta.meta_pdp_property_label
	    dl
	      dt.form_label Value
	      dd.form_label= @access_meta.meta_pdp_property_value
	.clearfix
	h5 General Information
	.general_information
	  .name-value-pairs
	    dl
	      dt.form_label Label
	      dd.form_label= @access_meta.meta_pdp_general_label
	    dl
	      dt.form_label Value
	      dd.form_label= @access_meta.meta_pdp_general_value
	.clearfix
	h5 School Information
	.school_information
	  .name-value-pairs
	    dl
	      dt.form_label Label
	      dd.form_label= @access_meta.meta_pdp_school_label
	    dl
	      dt.form_label Value
	      dd.form_label= @access_meta.meta_pdp_school_value
	.clearfix
	h5 Community Information
	.community_information
	  .name-value-pairs
	    dl
	      dt.form_label Label
	      dd.form_label= @access_meta.meta_pdp_community_label
	    dl
	      dt.form_label Value
	      dd.form_label= @access_meta.meta_pdp_community_value
	.clearfix
	h5 Lot Information
	.lot_information
	  .name-value-pairs
	    dl
	      dt.form_label Label
	      dd.form_label= @access_meta.meta_pdp_lot_label
	    dl
	      dt.form_label Value
	      dd.form_label= @access_meta.meta_pdp_lot_value
	.clearfix
	h5 Rooms Information
	.rooms_information
	  .name-value-pairs
	    dl
	      dt.form_label Label
	      dd.form_label= @access_meta.meta_pdp_rooms_label
	    dl
	      dt.form_label Value
	      dd.form_label= @access_meta.meta_pdp_rooms_value
	.clearfix
	h5 Location Information
	.location_information
	  .name-value-pairs
	    dl
	      dt.form_label Label
	      dd.form_label= @access_meta.meta_pdp_location_label
	    dl
	      dt.form_label Value
	      dd.form_label= @access_meta.meta_pdp_location_value
	.clearfix
	h5 Property Features
	.location_information
	  .name-value-pairs
	    dl
	      dt.form_label Label
	      dd.form_label= @access_meta.meta_pdp_features_label
	    dl
	      dt.form_label Value
	      dd.form_label= @access_meta.meta_pdp_features_value
      .clearfix

  div(style='margin-top: 10px;') Reminder: we used to let you copy these values to the MLS system with a button here.
    That was helpful because then you could copy them out to all other clients. But because we no longer allow such
    customizations, there's no need to copy.

  .features
    h3 Features
    .name-value-pairs
      dl
        dt.form_label Enable shortcode GUI
        dd.form_label= nbsp(@client.feature_gui.humanize)

  .tasks
    h3 Tasks
    .pfmls_idx_update
      h4 PFMLS IDX Update
      p
        | The following section allows you to run <code>?pfmls_idx_update</code> on the client websites. If there are no buttons here, it's because there are no URLs associated with this account. You may add some by
        =< link_to "editing this client", edit_client_path(@client)
        |  and adding to the URLs text box.
      = render "pfmls_idx_update", urls: @client.get_urls

  p &nbsp;

  = render "crms/widget"

  p
    a(href=clients_path)
      | &laquo; Return to clients list

  p &nbsp;
