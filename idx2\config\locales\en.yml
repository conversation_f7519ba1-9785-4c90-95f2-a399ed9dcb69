# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more, please read the Rails Internationalization guide
# available at http://guides.rubyonrails.org/i18n.html.

en:
  activerecord:
    attributes:
      client:
        access_account_id: "Account ID"
        access_apikey: "API key"
        access_member_id: "MLS member ID"
        access_office_id: "MLS office ID"
        access_youtube_id: "YouTube ID"
        mls: "MLS"
        domain: "Domain"
        ftp_addr: "FTP Address"
        ftp_user: "FTP User"
        ftp_pass: "FTP Password"
        access_company: "Company name"
        access_fullname: "Full name"
        access_address: "Address line 1"
        access_address2: "Address line 2"
        access_city: "City"
        access_state: "State"
        access_zipcode: "Zip code"
        access_phone: "Phone"
        access_emailaddress: "Email address"
        access_status: "Status"
        hide_ucb: "Hide UCB"
        URLs: "URLs"
        feature_gui: "Enable shortcode GUI"
        broker_office_name: "Broker office name"
      registration:
        market_area: "Market area"
        existing_website_url: "Existing website URL"
        # theme: "Region / MLS"
        facebook_url: "Facebook URL"
        linkedin_url: "LinkedIn URL"
        twitter_url: "Twitter URL"
        google_plus_url: "Google Plus URL"
        youtube_url: "YouTube URL"
        other_social_media: "Other social media"
        notes: "Notes"
        color_scheme: "Color scheme"
        title: "Title"
        tagline: "Tagline"
        headshot: "Headshot image"
        logo: "Logo image"
        website_state: "Website state"
        website_request: "Website request"
        website_response: "Website response"
        promo_code: "Promo code"
        epn_response: "ePN response"
        payment_state: "Payment state"
      access_meta:
        meta_prop_url: "URL"
        meta_prop_title: "Page Title"
        meta_prop_h1: "Page h1"
        meta_prop_keywords: "Keywords META"
        meta_prop_description: "Description META"
        meta_result_title: "Page Title"
        meta_result_h1: "Page h1"
        meta_result_keywords: "Keywords META"
        meta_result_description: "Description META"
        meta_result_prop_h2: "Property h2"
        meta_result_prop_content: "Property Content"
        meta_cat_title: "Page Title"
        meta_cat_h1: "Page h1"
        meta_cat_keywords: "Keywords META"
        meta_cat_description: "Description META"
        meta_disclosure: "Disclosure META"
        meta_address: "Address META"
        meta_street_address: "Street Address META"
        meta_prop_details: "Property Details META"
        # meta_geoapi: ""
        # meta_links_seo: ""
      mls_system:
        meta_prop_url: "URL"
        meta_prop_title: "Page Title"
        meta_prop_h1: "Page h1"
        meta_prop_keywords: "Keywords META"
        meta_prop_description: "Description META"
        meta_result_title: "Page Title"
        meta_result_h1: "Page h1"
        meta_result_keywords: "Keywords META"
        meta_result_description: "Description META"
        meta_result_prop_h2: "Property h2"
        meta_result_prop_content: "Property Content"
        meta_cat_title: "Page Title"
        meta_cat_h1: "Page h1"
        meta_cat_keywords: "Keywords META"
        meta_cat_description: "Description META"
        meta_disclosure: "Disclosure META"
        meta_address: "Address META"
        meta_street_address: "Street Address META"
        meta_prop_details: "Property Details META"
        # meta_geoapi: ""
        # meta_links_seo: ""
      reseller:
        friendlyid: "Friendly ID"
        display_name: "Display name"
        admin_email: "Admin email address"
        website_server: "Destination multisite URL"
        business_name: "Business name"
        slug: "Slug"
        payment_url: "Payment URL"
        website_url: "Website URL"
        signup_url: "Signup URL"
        monthly_rate: "Monthly rate"
        theme_previews_url: "Theme previews URL"
        sliders_url: "Sliders URL"
