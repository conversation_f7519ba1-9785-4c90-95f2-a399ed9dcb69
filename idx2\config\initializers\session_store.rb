# Be sure to restart your server when you modify this file.

# I'm specifying domain: :all here so that we can use the API at api.(domain), which is
# a subdomain and wouldn't normally be able to share cookies.
# Idx2::Application.config.session_store :cookie_store, key: '_idx2_session'
# Reminder: I'm not using localhost anymore because cookies require a dot in the domain.
domains = ['domain.com', 'ifoundagent.com']
domain_list = domains.dup
domains.each do |domain|
  domain_list.push "api.#{domain}"
end
Idx2::Application.config.session_store :cookie_store, key: '_idx2_session', domain: domain_list
