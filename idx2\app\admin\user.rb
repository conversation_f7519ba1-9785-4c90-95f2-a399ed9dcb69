ActiveAdmin.register User do
  index do
    column :email
    column :current_sign_in_at
    column :last_sign_in_at
    column :sign_in_count
    column :admin
    actions
  end

  filter :email
  filter :admin

  form do |f|
    f.inputs "Admin Details" do
      f.input :email
      f.input :password
      f.input :admin
    end
    f.actions
  end
  controller do
    def permitted_params
      params.permit user: [:email, :password, :admin]
    end
  end
end
