class AccessMetaDefaults
  attr_accessor :mls_name

  def self.factory(mls_name)
    AccessMetaDefaults.new(mls_name)
  end

  def initialize(mls_name)
    self.mls_name = mls_name
  end

  def set_defaults(meta)
    mls_system = MlsSystem.friendly.find(self.mls_name)

    # The AccessMeta table doesn't allow nulls. So we force everything to blanks by default, and only
    # set them to values if the value specified isn't null.

    meta.meta_prop_url = ""
    meta.meta_prop_title = ""
    meta.meta_prop_h1 = ""
    meta.meta_prop_keywords = ""
    meta.meta_prop_description = ""
    meta.meta_result_title = ""
    meta.meta_result_h1 = ""
    meta.meta_result_keywords = ""
    meta.meta_result_description = ""
    meta.meta_result_prop_h2 = ""
    meta.meta_result_prop_content = ""
    meta.meta_cat_title = ""
    meta.meta_cat_h1 = ""
    meta.meta_cat_keywords = ""
    meta.meta_cat_description = ""
    meta.meta_geoapi = ""
    meta.meta_links_seo = "" 
    meta.meta_disclosure = ""
    meta.meta_address = ""
    meta.meta_street_address = ""
    meta_pdp_property_label = ""
    meta_pdp_property_value = ""
    meta_pdp_general_label = ""
    meta_pdp_general_value = ""
    meta_pdp_school_label = ""
    meta_pdp_school_value = ""
    meta_pdp_community_label = ""
    meta_pdp_community_value = ""
    meta_pdp_lot_label = ""
    meta_pdp_lot_value = ""
    meta_pdp_rooms_label = ""
    meta_pdp_rooms_value = ""
    meta_pdp_location_label = ""
    meta_pdp_location_value = ""

    meta.meta_prop_url = mls_system.meta_prop_url if mls_system.meta_prop_url.present?
    meta.meta_prop_title = mls_system.meta_prop_title if mls_system.meta_prop_title.present?
    meta.meta_prop_h1 = mls_system.meta_prop_h1 if mls_system.meta_prop_h1.present?
    meta.meta_prop_keywords = mls_system.meta_prop_keywords if mls_system.meta_prop_keywords.present?
    meta.meta_prop_description = mls_system.meta_prop_description if mls_system.meta_prop_description.present?
    meta.meta_result_title = mls_system.meta_result_title if mls_system.meta_result_title.present?
    meta.meta_result_h1 = mls_system.meta_result_h1 if mls_system.meta_result_h1.present?
    meta.meta_result_keywords = mls_system.meta_result_keywords if mls_system.meta_result_keywords.present?
    meta.meta_result_description = mls_system.meta_result_description if mls_system.meta_result_description.present?
    meta.meta_result_prop_h2 = mls_system.meta_result_prop_h2 if mls_system.meta_result_prop_h2.present?
    meta.meta_result_prop_content = mls_system.meta_result_prop_content if mls_system.meta_result_prop_content.present?
    meta.meta_cat_title = mls_system.meta_cat_title if mls_system.meta_cat_title.present?
    meta.meta_cat_h1 = mls_system.meta_cat_h1 if mls_system.meta_cat_h1.present?
    meta.meta_cat_keywords = mls_system.meta_cat_keywords if mls_system.meta_cat_keywords.present?
    meta.meta_cat_description = mls_system.meta_cat_description if mls_system.meta_cat_description.present?
    meta.meta_geoapi = mls_system.meta_geoapi if mls_system.meta_geoapi.present?
    meta.meta_links_seo = mls_system.meta_links_seo if mls_system.meta_links_seo.present?
    meta.meta_disclosure = mls_system.meta_disclosure if mls_system.meta_disclosure.present?
    meta.meta_address = mls_system.meta_address if mls_system.meta_address.present?
    meta.meta_street_address = mls_system.meta_street_address if mls_system.meta_street_address.present?
    meta.meta_pdp_property_label = mls_system.meta_pdp_property_label if mls_system.meta_pdp_property_label.present?
    meta.meta_pdp_property_value = mls_system.meta_pdp_property_value if mls_system.meta_pdp_property_value.present?
    meta.meta_pdp_general_label = mls_system.meta_pdp_general_label if mls_system.meta_pdp_general_label.present?
    meta.meta_pdp_general_value = mls_system.meta_pdp_general_value if mls_system.meta_pdp_general_value.present?
    meta.meta_pdp_school_label = mls_system.meta_pdp_school_label if mls_system.meta_pdp_school_label.present?
    meta.meta_pdp_school_value = mls_system.meta_pdp_school_value if mls_system.meta_pdp_school_value.present?
    meta.meta_pdp_community_label = mls_system.meta_pdp_community_label if mls_system.meta_pdp_community_label.present?
    meta.meta_pdp_community_value = mls_system.meta_pdp_community_value if mls_system.meta_pdp_community_value.present?
    meta.meta_pdp_lot_label = mls_system.meta_pdp_lot_label if mls_system.meta_pdp_lot_label.present?
    meta.meta_pdp_lot_value = mls_system.meta_pdp_lot_value if mls_system.meta_pdp_lot_value.present?
    meta.meta_pdp_rooms_label = mls_system.meta_pdp_rooms_label if mls_system.meta_pdp_rooms_label.present?
    meta.meta_pdp_rooms_value = mls_system.meta_pdp_rooms_value if mls_system.meta_pdp_rooms_value.present?
    meta.meta_pdp_location_label = mls_system.meta_pdp_location_label if mls_system.meta_pdp_location_label.present?
    meta.meta_pdp_location_value = mls_system.meta_pdp_location_value if mls_system.meta_pdp_location_value.present?

    meta
  end
end
