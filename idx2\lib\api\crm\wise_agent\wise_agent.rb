module Api
  module Crm
    module WiseAgent
      class WiseAgent < CrmApi
        self.display_name = 'Wise Agent'
        include HTTParty
        base_uri 'https://sync.thewiseagent.com'

        def handles_event?(metadata, data)
          get_event_handler(metadata, data) ? true : super
        end

        def handle_event(metadata, data)
          myproc = get_event_handler metadata, data
          myproc ? myproc.call : super
        end

        def get_event_handler(metadata, data)
          case metadata['event_name']
          when 'gform_after_submission'
            wa = GravityForms::Crm::WiseAgent.new
            return wa.get_event_handler metadata, data, self
          end
        end

        def meant_for_job_queue?(metadata, data)
          # True until we introduce real-time communication.
          true
        end

        def create_contact(contact_params)
          contact_params = self.sanitize contact_params

          # A reminder about WiseAgent's webcontact api call: It seems to be
          # keyed on names and email. If I submit the same request twice with
          # only the email/name address changed, it will create a new contact.
          # If I change only the phone number, it will not create a new
          # contact, and it will NOT update the phone number, but it WILL add
          # the 'Message' field as a contact note in the same way it does when
          # it does create a new contact.
          request_params = contact_params.merge({
            requestType: 'webcontact',
            noNotify: '1',
            nomail: '1',
            Categories: 'General'
          })

          all_params = add_apikey_to_request request_params
          Rails.logger.info('WiseAgent') { "About to post to WiseAgent: #{all_params}" }
          # Reminder; It seems like not all of the data should be in the
          # query. However, unless it is, I get messages like this (from Wise
          # Agent) for each field until I move said field into the query:
          # [Microsoft][ODBC SQL Server Driver][SQL Server]Procedure or
          # function 'ImportClient' expects parameter '@CFirst', which was not
          # supplied.
          params = { query: all_params }
          response = self.post params
          Rails.logger.info('WiseAgent') { "Received response from WiseAgent: #{response}" }
          response
        end

        # Wise Agent does not sanitize its input, so we must.
        def sanitize(hash)
          hash.merge!(hash) { |k, v| Sanitize.fragment v }
        end

        def add_apikey_to_request(params)
          params.merge apikey: self.apikey
        end

        def route
          '/http/webconnect.asp'
        end

        def post(params)
          self.class.post route, params
        end
      end
    end
  end
end
