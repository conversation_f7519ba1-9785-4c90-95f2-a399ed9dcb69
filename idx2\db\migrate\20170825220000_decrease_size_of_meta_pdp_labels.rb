class DecreaseSizeOfMetaPdpLabels < ActiveRecord::Migration
  def up
    change_column :mls_systems, :meta_pdp_property_label, :string, :default => 'Property Description', limit: 100
    change_column :mls_systems, :meta_pdp_general_label, :string, :default => 'General Information', limit: 100
    change_column :mls_systems, :meta_pdp_school_label, :string, :default => 'School Information', limit: 100
    change_column :mls_systems, :meta_pdp_community_label, :string, :default => 'Community Information', limit: 100
    change_column :mls_systems, :meta_pdp_lot_label, :string, :default => 'Lot Information', limit: 100
    change_column :mls_systems, :meta_pdp_rooms_label, :string, :default => 'Rooms Information', limit: 100
    change_column :mls_systems, :meta_pdp_location_label, :string, :default => 'Property Description', limit: 100
    change_column :access_meta, :meta_pdp_property_label, :string, :default => 'Property Description', limit: 100
    change_column :access_meta, :meta_pdp_general_label, :string, :default => 'General Information', limit: 100
    change_column :access_meta, :meta_pdp_school_label, :string, :default => 'School Information', limit: 100
    change_column :access_meta, :meta_pdp_community_label, :string, :default => 'Community Information', limit: 100
    change_column :access_meta, :meta_pdp_lot_label, :string, :default => 'Lot Information', limit: 100
    change_column :access_meta, :meta_pdp_rooms_label, :string, :default => 'Rooms Information', limit: 100
    change_column :access_meta, :meta_pdp_location_label, :string, :default => 'Property Description', limit: 100
    change_column :mls_systems, :meta_pdp_main_label, :string, :default => 'PDP Main Content', limit: 100
    change_column :mls_systems, :meta_pdp_features_label, :string, :default => 'Property Features', limit: 100
    change_column :access_meta, :meta_pdp_main_label, :string, :default => 'PDP Main Content', limit: 100
    change_column :access_meta, :meta_pdp_features_label, :string, :default => 'Property Features', limit: 100

  end

  def down
    change_column :mls_systems, :meta_pdp_property_label, :string, :default => 'Property Description', limit: 255
    change_column :mls_systems, :meta_pdp_general_label, :string, :default => 'General Information', limit: 255
    change_column :mls_systems, :meta_pdp_school_label, :string, :default => 'School Information', limit: 255
    change_column :mls_systems, :meta_pdp_community_label, :string, :default => 'Community Information', limit: 255
    change_column :mls_systems, :meta_pdp_lot_label, :string, :default => 'Lot Information', limit: 255
    change_column :mls_systems, :meta_pdp_rooms_label, :string, :default => 'Rooms Information', limit: 255
    change_column :mls_systems, :meta_pdp_location_label, :string, :default => 'Property Description', limit: 255
    change_column :access_meta, :meta_pdp_property_label, :string, :default => 'Property Description', limit: 255
    change_column :access_meta, :meta_pdp_general_label, :string, :default => 'General Information', limit: 255
    change_column :access_meta, :meta_pdp_school_label, :string, :default => 'School Information', limit: 255
    change_column :access_meta, :meta_pdp_community_label, :string, :default => 'Community Information', limit: 255
    change_column :access_meta, :meta_pdp_lot_label, :string, :default => 'Lot Information', limit: 255
    change_column :access_meta, :meta_pdp_rooms_label, :string, :default => 'Rooms Information', limit: 255
    change_column :access_meta, :meta_pdp_location_label, :string, :default => 'Property Description', limit: 255
    change_column :mls_systems, :meta_pdp_main_label, :string, :default => 'PDP Main Content', limit: 255
    change_column :mls_systems, :meta_pdp_features_label, :string, :default => 'Property Features', limit: 255
    change_column :access_meta, :meta_pdp_main_label, :string, :default => 'PDP Main Content', limit: 255
    change_column :access_meta, :meta_pdp_features_label, :string, :default => 'Property Features', limit: 255
  end
end
