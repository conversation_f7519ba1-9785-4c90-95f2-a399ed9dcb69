class MlsSystem < ActiveRecord::Base
  has_many :clients, foreign_key: "mls", primary_key: "name"
  default_scope { order('name') }

  include FriendlyId
  friendly_id :name

  # Copies all SEO values from this record to the access_meta record of each client of the same MLS.
  def copy_to_clients
    field_values_map = {}
    self.class.fields_to_copy.each do |field|
      field_values_map[field] = self[field]
    end
    # I'd prefer 'access' in the following to be 'clients', but I don't know
    # how to get rails to  recognize the table name alias.
    # See http://stackoverflow.com/questions/2961830/include-and-table-aliasing
    # and https://github.com/rails/rails/issues/606.
    AccessMeta.joins(:client).where(access: { mls: self.name }).update_all field_values_map
  end

  # Copies template values to access_meta of all clients with the same MLS
  def copy_template_to_clients
    field_values_map = {}
    self.class.template_fields_to_copy.each do |field|
      field_values_map[field] = self[field]
    end
    AccessMeta.joins(:client).where(access: { mls: self.name }).update_all field_values_map
  end

  def copy_from_client(client)
    access_meta = client.access_meta
    self.class.fields_to_copy.each do |field|
      self[field] = access_meta[field]
    end
    save!
  end

  # used for copying seo fields only
  def self.fields_to_copy
    [
      :meta_prop_url,
      :meta_prop_title,
      :meta_prop_h1,
      :meta_prop_keywords,
      :meta_prop_description,
      :meta_result_title,
      :meta_result_h1,
      :meta_result_keywords,
      :meta_result_description,
      :meta_result_prop_h2,
      :meta_result_prop_content,
      :meta_cat_title,
      :meta_cat_h1,
      :meta_cat_keywords,
      :meta_cat_description,
      :meta_geoapi,
      :meta_links_seo
    ]
  end

  # used for copying template fields only
  def self.template_fields_to_copy
    [
      :meta_disclosure,
      :meta_address,
      :meta_street_address,
      :meta_pdp_main_label,
      :meta_pdp_main_value,
      :meta_pdp_property_label,
      :meta_pdp_property_value,
      :meta_pdp_general_label,
      :meta_pdp_general_value,
      :meta_pdp_school_label,
      :meta_pdp_school_value,
      :meta_pdp_community_label,
      :meta_pdp_community_value,
      :meta_pdp_lot_label,
      :meta_pdp_lot_value,
      :meta_pdp_rooms_label,
      :meta_pdp_rooms_value,
      :meta_pdp_location_label,
      :meta_pdp_location_value,
      :meta_pdp_features_label,
      :meta_pdp_features_value
    ]
  end
end
