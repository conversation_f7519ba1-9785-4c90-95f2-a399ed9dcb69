require 'sidekiq/web'

Idx2::Application.routes.draw do
  ActiveAdmin.routes(self)
  devise_for :users
  # See https://github.com/mperham/sidekiq/wiki/Monitoring
  authenticate :user, lambda { |u| u.admin? } do
    mount Sidekiq::Web, at: '/sidekiq'
  end

  root to: "pages#main"

  # In the following, the subdomain constraint is purposefully misspelled such
  # that it does not take effect. I do this to leave it as a reminder of what
  # I want, but I leave it off because Rails can't test routing when you need
  # to specify both a subdomain and a method. I either have to not have
  # the constraint, or not have the testing. I've chosen having the testing,
  # which means that the subdomain constraint isn't a constraint at all.
  scope module: 'api', constraints: { ubdomain: /^api$/ }, defaults: { format: :json } do
    resources :registrations, only: [:show] do
      post :build
      post :reset
    end
    namespace :crm do
      namespace :events do
        # The route 'dispatch' here makes rails barf with:
        # ArgumentError - wrong number of arguments (2 for 0)
        # I assume the method named dispatch is 'taken' by rails core.
        post :go
      end
    end
    resources :field_mappings
    namespace :wordpress_networks do
      post '', to: :create
    end
  end

  resources :clients do
    patch 'seo'
    get 'registration'
    collection do
      get :new_idx_access_only
      post :create_idx_access_only
    end
    resources :client_crms, module: 'api', defaults: { format: :json }
  end
  resources :videos

  scope :path => '/', :controller => :pages do
    get 'must_be_signed_out'
    get 'registration_success'
    get 'registration_success_idx_access_only'
    get 'must_be_client'
    match 'payment_approved', via: [:get, :post]
    match 'payment_declined', via: [:get, :post]
    # As a reminder, this is meant to be for resellers, where the user doesn't
    # need to be logged in (as we would require for our own users, via the
    # secure_pages controller).
    get 'pay'
  end

  # namespace :secure_pages do
  scope '/secure_pages', controller: :secure_pages do
    get 'pay'
  end

  if Rails.env.development?
    mount MailPreview => 'mail_view'
  end

  namespace :custom_admin do
    resources :videos
  end

  scope "/production_tests", controller: :production_tests do
    get '/' => "production_tests#index", as: "production_tests"
    get 'email_test'
  end

  resources :mls_systems
  resources :resellers do
    resources :reseller_pricings
  end
end
