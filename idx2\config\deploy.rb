require "bundler/capistrano"
require 'capistrano/sidekiq'

def set_default(name, *args, &block)
  set(name, *args, &block) unless exists?(name)
end

load "config/recipes/check"
load "config/recipes/db"
load "config/recipes/extras"
load "config/recipes/unicorn"

set :application, "idx2"
set :repository,  "*****************:/profoundidx/profoundidx.git"
set :branch, "master"
# See: http://stackoverflow.com/a/2047574/135101
set :deploy_subdir, "idx2"
set :scm, :git # You can set :scm explicitly or Capistrano will make an intelligent guess based on known version control directory names
# Or: `accurev`, `bzr`, `cvs`, `darcs`, `git`, `mercurial`, `perforce`, `subversion` or `none`
set :user, "ubuntu"
set :use_sudo, false

server "profoundidx.com", :web, :app, :resque_worker, :db, primary: true

# if you want to clean up old releases on each deploy uncomment this:
after "deploy:restart", "deploy:cleanup"

set :deploy_to, "/home/<USER>/apps/#{application}"
# See: http://stackoverflow.com/a/9710542/135101
set :shared_children, shared_children + %w{public/uploads}

# See: http://stackoverflow.com/a/3643914/135101
set :ssh_options, forward_agent: true
# See: https://help.github.com/articles/deploying-with-capistrano
set :deploy_via, :remote_cache
default_run_options[:pty] = true
after "deploy", "deploy:cleanup" # keep only the last 5 releases
after "deploy:migrations", "deploy:cleanup"
