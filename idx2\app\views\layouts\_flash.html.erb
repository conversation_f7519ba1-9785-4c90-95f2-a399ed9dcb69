<% for key in flash.keys %>
  <% key = key.to_sym %>
  <%
    case key
      when :alert, :error
        class_suffix = "error"
      when :success
        class_suffix = "success"
      when :warning
        class_suffix = "warning"
      else
        class_suffix = "info"
    end
  %>
  <div class='alert alert-<%= class_suffix %>'>
    <a class="close" data-dismiss="alert" href="#">×</a>
    <% if flash[key].respond_to?(:each) %>
      <ul>
        <% flash[key].each do |msg| %>
          <li><%= msg %></li>
        <% end %>
      </ul>
    <% else %>
      <%= flash[key] %>
    <% end %>
  </div>
<% end %>
