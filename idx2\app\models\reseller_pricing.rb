class ResellerPricing < ActiveRecord::Base
  belongs_to :reseller

  extend FriendlyId
  friendly_id :promo_code, use: [:slugged, :finders]

  def should_generate_new_friendly_id?
    val = promo_code.blank? || promo_code_changed?
  end

  # Having both reseller_id and reseller here is currently only for the sake of testing
  # from the registration mailer, where records are created with 'build_stubbed'.
  def self.compute_payment_url(reseller_id: nil, reseller: nil, promo_code: 'LATER')
    if reseller_id || reseller
      if promo_code == 'LATER'
        # This will be the case if the reseller hasn't had reseller pricings defined, e.g. resellers we haven't updated yet.
        _reseller = reseller || Reseller.friendly.find(reseller_id)
        return _reseller.payment_url
      else
        reseller_pricing = ResellerPricing.friendly.find promo_code
        return reseller_pricing.payment_url
      end
    else
      "http://ifoundagent.com/pay-for-getfoundidx"
    end
  end
end
