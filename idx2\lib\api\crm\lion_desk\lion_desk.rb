module Api
  module Crm
    module LionDesk
      class LionDesk < CrmApi
        self.display_name = 'LionDesk'
        include HTTParty
        base_uri 'https://api-v1.liondesk.com'

        def handles_event?(metadata, data)
          get_event_handler(metadata, data) ? true : super
        end

        def handle_event(metadata, data)
          myproc = get_event_handler metadata, data
          myproc ? myproc.call : super
        end

        def get_event_handler(metadata, data)
          case metadata['event_name']
          when 'gform_after_submission'
            crm = GravityForms::Crm::LionDesk.new
            return crm.get_event_handler metadata, data, self
          when 'Pfmls::NewActivity'
            return -> () { newactivity metadata, data }
          when 'wordpress_user_register'
            return -> () { wordpress_user_register metadata, data }
          else
            puts "Unsupported metadata #{metadata['event_name']}"
          end
        end

        def meant_for_job_queue?(metadata, data)
          # True until we introduce real-time communication.
          true
        end

        def wordpress_user_register(metadata, data)
          contact_params = {
            contactid: data['username'],
            email: data['email'],
            siteid: data['network_id']
          }
          newsubmission contact_params
        end

        def newsubmission(contact_params)
          contact_params = self.sanitize contact_params

          json_body = contact_params.merge({ action: 'NewSubmission' })
          post_to_liondesk json_body
        end

        def newactivity(metadata, data)
          json_body = data.merge({ action: 'NewActivity' })
          post_to_liondesk json_body
        end

        def post_to_liondesk(json_body)
          Rails.logger.info(self.display_name) { "About to post to #{self.display_name} for client with API key #{self.apikey}: #{json_body}" }
          params = {
            body: json_body.to_json,
            basic_auth: {
              username: Rails.configuration.liondesk.apikey,
              password: ''
            },
            headers: {
              "X-LionDesk-Id" => self.apikey
            }
          }
          response = self.post params
          Rails.logger.info(self.display_name) { "Received response from #{self.display_name} for client with API key #{self.apikey}: #{response}" }
          response
        end

        # LionDesk does not sanitize its input, so we must.
        def sanitize(hash)
          hash.merge!(hash) { |k, v| Sanitize.fragment v }
        end

        def route
          '/'
        end

        def post(params)
          self.class.post route, params
        end
      end
    end
  end
end
