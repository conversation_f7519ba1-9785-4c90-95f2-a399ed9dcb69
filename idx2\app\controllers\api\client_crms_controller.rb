module Api
  class ClientCrmsController < ApplicationController
    before_filter :authenticate_custom_admin_user!
    respond_to :json
    self.responder = ApiResponder

    def index
      respond_with ClientCrm.where(access_id: params[:client_id])
    end

    def create
      @client = Client.find(params[:client_id])
      client_crm = @client.client_crms.build create_params
      client_crm.sync_display_name
      client_crm.save
      respond_with client_crm
    end

    def destroy
      @client_crm = ClientCrm.find(params[:id])
      @client_crm.destroy!
      respond_with @client_crm
    end

    private

      def create_params
        params.permit(
          :computer_name,
          :apikey,
          :enabled,
          :primary
        )
      end
  end
end
