set_default(:unicorn_user) { user }
set_default(:unicorn_pid) { "#{current_path}/tmp/pids/unicorn.pid" }
set_default(:unicorn_config) { "#{shared_path}/config/unicorn.rb" }
set_default(:unicorn_log) { "#{shared_path}/log/unicorn.log" }
set_default(:unicorn_workers, 2)

namespace :unicorn do
  desc "Setup Unicorn initializer and app configuration"
  task :setup, roles: :app do
    run "mkdir -p #{shared_path}/config"
    template "unicorn.erb.rb", unicorn_config
    template "unicorn_init.erb.sh", "/tmp/unicorn_init"
    run "chmod +x /tmp/unicorn_init"
    run "#{sudo} mv /tmp/unicorn_init /etc/init.d/unicorn_#{application}"
    run "#{sudo} update-rc.d -f unicorn_#{application} defaults"
  end
  after "deploy:setup", "unicorn:setup"

  %w[start stop].each do |command|
    desc "#{command} unicorn"
    task command, roles: :app do
      run "#{sudo} /etc/init.d/unicorn_#{application} #{command}"
    end
    after "deploy:#{command}", "unicorn:#{command}"
  end

  # There is a bug in unicorn where restart doesn't actually behave the same as a start and stop as far as loading gems. The best option is to really stop and then start.
  desc "restart unicorn"
  task 'restart', roles: :app do
    run "#{sudo} /etc/init.d/unicorn_#{application} stop"
    run "#{sudo} /etc/init.d/unicorn_#{application} start"
  end
  after "deploy:restart", "unicorn:restart"
end
