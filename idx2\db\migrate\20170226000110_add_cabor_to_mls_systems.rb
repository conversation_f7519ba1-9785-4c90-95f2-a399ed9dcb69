class AddCaborToMlsSystems < ActiveRecord::Migration
  def up
    meta_result_prop_content = <<END
<div class="pfmls_prop_data">
  <ul>
    <li class="list_price">{ListPrice}</li>
    <li class="beds_and_baths">Bedrooms: {Beds}, Baths: {Bathrooms}</li>
    <li class="home_size">Home size: {SquareFeet}</li>
    <li class="lot_size">Lot size: {LotSquareFeet}</li>
    <li class="year_built">Year built: {YearBuilt}</li>
    <li class="mls_number">MLS #: {ListingID}</li>
  </ul>
</div>
END
    props = {
      name: "cabor",
      display_name: "CABOR",
      meta_prop_url: "{StreetNumber}-{StreetName}-{StreetSuffix}-{City}-{ListingArea}",
      meta_prop_title: "{StreetNumber} {StreetName} {StreetSuffix} {City} {ListingArea} {ListingID}",
      meta_prop_h1: "{StreetNumber} {StreetName} {StreetSuffix} {City} {ListingArea} {ListingID}",
      meta_prop_keywords: "",
      meta_prop_description: "{StreetNumber} {StreetName} {StreetSuffix} {City} - {Remarks} Home for Sale ",
      meta_result_title: "",
      meta_result_h1: "",
      meta_result_keywords: "",
      meta_result_description: "",
      meta_result_prop_h2: "{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber} {City} {PostalCode}, {Subdivision}",
      meta_result_prop_content: meta_result_prop_content,
      meta_cat_title: "{category} in {City}",
      meta_cat_h1: "{category} in {City}",
      meta_cat_keywords: "",
      meta_cat_description: "{category} in {City}",
      meta_geoapi: "",
      meta_links_seo: "",
    }
    MlsSystem.create props
  end

  def down
    MlsSystem.where(name: "cabor").destroy_all
  end
end
