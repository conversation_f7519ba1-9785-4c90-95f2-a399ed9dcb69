.reseller_pricings_show_page

  h1= @reseller_pricing.promo_code

  p
    = link_to '&laquo; Return to reseller pricings list'.html_safe, [@reseller, :reseller_pricings]

  p= link_to "Edit", edit_reseller_reseller_pricing_path(reseller_id: @reseller.friendly_id, id: @reseller_pricing.slug), class: "btn btn-primary btn-large edit-button-padding"

  .name-value-pairs
    dl
      dt.form_label Promo code
      dd.form_label= nbsp(@reseller_pricing.promo_code)
    dl
      dt.form_label Theme
      dd.form_label= nbsp(@reseller_pricing.theme)
    dl
      dt.form_label Recurring Method ID
      dd.form_label= nbsp(@reseller_pricing.recurring_method_id)
    dl
      dt.form_label Monthly Rate
      dd.form_label= nbsp(@reseller_pricing.monthly_rate)
    dl
      dt.form_label Pay URL
      dd.form_label= nbsp(@reseller_pricing.payment_url)
