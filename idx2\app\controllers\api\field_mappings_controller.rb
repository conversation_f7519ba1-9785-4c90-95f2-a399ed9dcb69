module Api
  class FieldMappingsController < ApplicationController
    before_filter :authenticate_custom_admin_user!
    respond_to :json

    def index
      respond_with FieldMapping.order(MapName: :asc, mls_class: :asc)
    end

    def create
      field_mapping = FieldMapping.create create_params
      respond_with field_mapping
    end

    def update
      field_mapping = FieldMapping.find params[:id]
      field_mapping.update create_params
      respond_with field_mapping
    end

    def show
      field_mapping = FieldMapping.find params[:id]
      respond_with field_mapping
    end

    def destroy
      field_mapping = FieldMapping.find params[:id]
      field_mapping.delete
      respond_with field_mapping
    end

    private

      def create_params
        params.permit(
          :mls_class,
          :MapName,
          :DisplayName,
          :EasyName,
          :Type,

          :armls,
          :trendmls,
          :brightmls,
          :sndmls,
          :paaraz,
          :paaraz_mlg,
          :sdcrca,
          :EasyName,
          :glvarnv,
          :tarmlsaz,
          :mredil,
          :naar,
          :wmar,
          :cabor,
          :recolorado,
          :recolorado_mlg,
          :wardex,
          :crmls,
          :realtracs,
          :armls_spark,
          :naar_spark,
        )
      end
  end
end
