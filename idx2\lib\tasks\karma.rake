namespace :karma  do
  task :start => :environment do
    with_tmp_config :start
  end

  task :run => :environment do
    with_tmp_config :start, "--single-run"
  end

  private

  def with_tmp_config(command, args = nil)
    # Force the temp file name to end in .coffee. Otherwise karma will assume
    # it's meant to be javascript.
    Tempfile.open(['karma_unit', '.coffee'], Rails.root.join('tmp') ) do |f|
      f.write unit_js(application_spec_files)
      f.flush

      system "./node_modules/.bin/karma #{command} --log-level debug #{f.path} #{args}"
    end
  end

  def application_spec_files
    sprockets = Rails.application.assets
    sprockets.append_path Rails.root.join("spec/karma")
    files = Rails.application.assets.find_asset("application_spec.js").to_a.map {|e| e.pathname.to_s }
  end

  def unit_js(files)
    unit_js = File.open('spec/karma/config/unit.coffee', 'r').read
    unit_js.gsub "APPLICATION_SPEC_FILES", "\"#{files.join("\",\n\"")}\""
  end
end