@echo off
echo 🚀 Starting Pro-Found IDX System...
cd /d "%~dp0"

echo 📦 Starting Docker containers...
docker-compose -f docker-compose.simple.yml up -d

echo ⏳ Waiting for containers to start...
timeout /t 15 /nobreak > nul

echo 🔧 Starting API server...
for /f "tokens=*" %%i in ('docker ps --filter "name=newidx" --format "{{.Names}}"') do set CONTAINER_NAME=%%i
if "%CONTAINER_NAME%"=="" (
    echo ❌ Error: Could not find newidx container
    echo 💡 Try running: docker-compose -f docker-compose.simple.yml ps
    pause
    exit /b 1
)

echo 📋 Using container: %CONTAINER_NAME%
docker cp server/database-idx-server.js %CONTAINER_NAME%:/www/database-idx-server.js
docker exec -d %CONTAINER_NAME% sh -c "cd /www && node database-idx-server.js > /tmp/idx-server.log 2>&1"

echo ⏳ Waiting for API server to start...
timeout /t 5 /nobreak > nul

echo 🧪 Testing system...
curl http://localhost:8155/health

echo.
echo ✅ Pro-Found IDX System Started!
echo 🌐 Web Dashboard: http://localhost
echo 🚀 API Server: http://localhost:8155
echo 🔍 Solr Admin: http://localhost:8984/solr
echo 🗄️ Database: localhost:33068
echo.
echo 📋 Quick Commands:
echo   - Quick restart: quick-restart-profoundidx.bat
echo   - View logs: docker logs %CONTAINER_NAME%
echo   - Stop system: docker-compose -f docker-compose.simple.yml down
echo.
pause
