module ApplicationHelper
  def title
    return @title if @override_title
    @title ? "#{@title} - Profound IDX Admin" : "Profound IDX Admin"
  end

  def flash_notice_exists
    flash.keys.size > 0
  end

  def output_flash
    render('layouts/flash', :flash => flash)
  end

  def output_flash_if_needed
    output_flash if flash_notice_exists
  end

  def theme_list
    theme_map = Registration.get_theme_to_color_schemes_map
    theme_map.keys.join ', '
  end
end
