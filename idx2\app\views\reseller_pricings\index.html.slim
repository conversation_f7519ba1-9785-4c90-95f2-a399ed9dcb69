h1= @reseller.display_name

p= link_to 'Back to reseller', @reseller

h2 Reseller Pricings

- if @reseller_pricings.size == 0
  p= "No reseller pricing records yet!"
- else
  table.table.table-striped.table-bordered.auto-width
    thead
      tr
        th ID
        th Promo code
        th Theme
        th Recurring Method ID
        th Monthly Rate
        th Pay URL
        th colspan="3"

    tbody
      - @reseller_pricings.each do |r|
        tr
          td= r.id
          td= r.promo_code
          td= r.theme
          td= r.recurring_method_id
          td= r.monthly_rate
          td= r.payment_url
          td= link_to 'Show', reseller_reseller_pricing_path(reseller_id: @reseller.friendly_id, id: r.slug)
          td= link_to 'Edit', edit_reseller_reseller_pricing_path(reseller_id: @reseller.friendly_id, id: r.slug)
          td= link_to 'Destroy', reseller_reseller_pricing_path(reseller_id: @reseller.friendly_id, id: r.slug), method: :delete, data: { confirm: "Are you sure?" }

p = link_to 'New reseller pricing', new_reseller_reseller_pricing_path
