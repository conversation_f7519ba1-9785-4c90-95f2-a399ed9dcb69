div ng-controller="regCtrl" ng-init="registrationId=#{registration.id}"
  div cg-busy="{promise:myPromise,message:'Loading Your Data'}"
  .status
    p
      | Website status:
      span.stat< ng-model="status" ng-init="status='#{registration.website_state}';" {{status}}
      button.btn.btn-info< ng-click="refresh()" Refresh
  .advance
    p
      | Click here to build website
      button.btn.btn-warning< ng-click="build()" Build
  .reset
    p
      | Click here to reset website state
      a.btn.btn-danger< ng-click="reset()" Reset
