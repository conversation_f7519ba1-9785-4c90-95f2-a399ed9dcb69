class AddGeocodingsTable < ActiveRecord::Migration
  def change
    create_table :geocodings do |t|
      t.string :address
      t.text :response_body
      t.string :referer

      t.timestamps
    end

    add_index :geocodings, :address, unique: true

    create_table :geocoding_failures do |t|
      t.integer :geocoding_id, null: false
      t.text :response_body
      t.string :referer

      t.timestamps
    end

    add_index :geocoding_failures, :geocoding_id
  end
end
