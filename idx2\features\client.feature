Feature: Manage clients
  In order to be able to manage clients
  I want to be able list and delete them

  @allow-rescue
  Scenario: Clients not shown to non-admin
    Given I'm logged in as client
    When I go to the list of clients
    Then I should see "not authorized"

  Scenario: Clients link not shown to non-admin
    Given I'm logged in as client
    When I go to the home page
    Then I should not see "Clients"

  @focus
  Scenario: Client list
    Given I have clients named <PERSON>, <PERSON>
    Given I'm logged in as admin
    When I go to the list of clients
    Then I should see "<PERSON>"
    And I should see "<PERSON>"

  @javascript
  Scenario: Delete client
    Given I have clients named <PERSON>, <PERSON>
    Given I'm logged in as admin
    When I go to the list of clients
    Then I should see "<PERSON>"
    When in the row containing <PERSON>, I click on delete 
    And I accept the confirmation dialog
    Then I should not see "<PERSON>"
    And I should see "<PERSON>"
    And the number of clients should be 1
