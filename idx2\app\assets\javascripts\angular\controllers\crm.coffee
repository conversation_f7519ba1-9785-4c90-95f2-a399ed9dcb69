module = angular.module 'pfmlsControllers'
module.controller "CrmsCtrl", ['$scope', 'Restangular', 'notification', 'crm', 'ngTableParams', '$filter', ($scope, Restangular, notification, crm, ngTableParams, $filter) ->
  form = {
    list: []
  }
  $scope.form = form
  params =
    count: 0
    sorting:
      display_name: 'asc'
      apikey: 'asc'
  settings =
    getData: ($defer, params) =>
      p = crm.getAll($scope.clientId)
      $scope.myPromise = p
      p.then (crms) =>
        filteredData = if params.filter() then $filter('filter')(crms, params.filter()) else crms
        orderedData = if params.sorting() then $filter('orderBy')(crms, params.orderBy()) else filteredData
        $scope.list = orderedData
        params.total orderedData.length
        $defer.resolve(orderedData)
  $scope.tableParams = new ngTableParams params, settings
  $scope.add = ->
    $scope.create
      computer_name: form.computerName
      apikey: form.apikey
  $scope.create = (data) ->
    data.enabled = true
    q = Restangular.one "clients", $scope.clientId
    p = q.post 'client_crms', data
    $scope.myPromise = p
    success = (clientCrm) ->
      form.list.push clientCrm.plain()
      notification.success "Added CRM connection"
      $scope.clearForm()
      $scope.tableParams.reload()
    fail = (obj) ->
      msg = obj?.data?.message
      notification.error msg, "Failed to add CRM connection"
    p.then(success).catch(fail)
  $scope.clearForm = ->
    form.computerName = ""
    form.apikey = ""
  $scope.delete = (crm) ->
    p = crm.remove()
    $scope.myPromise = p
    p.then ->
      $scope.tableParams.reload()
    .catch ->
      notification.error "Failed to delete CRM connection"
]
