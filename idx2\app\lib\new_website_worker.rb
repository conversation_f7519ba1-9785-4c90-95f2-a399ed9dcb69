class NewWebsiteWorker
  include Sidekiq::Worker
  sidekiq_options :retry => 0

  # Reminder: states in `state machine` are stored as strings.
  # https://github.com/pluginaweek/state_machine#symbols-vs-strings
  def perform(registration_id, password)
    logger.info('NewWebsiteWorker') { "Registration id: #{registration_id}" }
    registration = Registration.find registration_id
    logger.info('NewWebsiteWorker') { "About to progress the new website worker from state: #{registration.website_state}" }
    registration.progress_website password: password
  end
end
