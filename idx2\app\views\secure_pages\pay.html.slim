.page.pay
  h1 Pay

  p Need to pay for your website? This is the place.

  - if current_user.has_paid?
    .label.label-info Sweet!
    p Our records show you've already paid.
  - else
    p Please use the submit button below. You'll be taken to our payment provider's website, ePN (e-Processing Network). After your payment is processed, you'll be returned to this website.

    / = form_tag "https://www.eprocessingnetwork.com/cgi-bin/Reflect/order.pl" do
    = form_tag Rails.configuration.ePN.order_url do
      = hidden_field_tag :ePNAccount, Rails.configuration.ePN.account_number
      / = hidden_field_tag :ePNAccount, "080880"
      = hidden_field_tag :Total, @registration.compute_setup_payment
      = hidden_field_tag :EMail, @client.email
      = hidden_field_tag :ID, @registration.id
      = hidden_field_tag :ReturnApprovedURL, payment_approved_url
      = hidden_field_tag :ReturnDeclinedURL, payment_declined_url
      / We don't need to use these fields, but this is a reminder that they exist.
      /= hidden_field_tag :BackgroundColor, ""
      /= hidden_field_tag :TextColor, ""
      = hidden_field_tag :LogoURL, Rails.configuration.ePN.logo_url
      / = hidden_field_tag :email, "<EMAIL>"
      / = hidden_field_tag :PostUrl, "https://www.eProcessingNetwork.com/cgi-bin/dbe/order.pl"

      = submit_tag "Submit and fill out payment info with e-Processing Network", class: "btn btn-primary"
