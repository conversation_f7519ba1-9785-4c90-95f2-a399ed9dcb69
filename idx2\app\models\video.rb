class Video < ActiveRecord::Base
  belongs_to :client, foreign_key: "access_id"

  validates_presence_of :access_id
  validates_presence_of :MLS_id
  validates_numericality_of :MLS_id
  validates_presence_of :youtube_id

  # Save a copy of this video against all clients for this user
  def save_for_user(user)
    Video.transaction do
      user.clients.each do |client|
        vid = self.dup
        vid.client = client
        vid.save!
      end
    end
  end

  # Delete this video for all clients sites of user
  def delete_for_user(user)
    user.clients.each do |client|
      client.videos.where(MLS_id: self.MLS_id, youtube_id: self.youtube_id).destroy_all
    end
  end
end