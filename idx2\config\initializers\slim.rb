# See: https://github.com/slim-template/slim, search for "Attribute shortcuts"
# In this example we add & to create a shortcut for the input elements with type attribute.
Slim::Engine.set_options shortcut: {'&' => {:tag => 'input', :attr => 'type'}, '#' => {:attr => 'id'}, '.' => {:attr => 'class'}}
# Change the default attribute delimiters. This allows us to more cleanly/neatly use {{}} with AngularJS.
# See: http://stackoverflow.com/questions/17687389/slim-template-interprets-myjsvar-as-html-attribute-grouping#comment43276294_17935603
Slim::Engine.set_options :attr_list_delims => {'(' => ')', '[' => ']'}
