<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title><%= @subject %></title>
</head>
<body leftmargin="0" marginwidth="0" topmargin="0" marginheight="0" offset="0" style="-webkit-text-size-adjust: none;margin: 0;padding: 0;background-color: #FAFAFA;width: 100% !important;">
<center>
  <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="backgroundTable" style="margin: 0;padding: 0;background-color: #FAFAFA;height: 100% !important;width: 100% !important;">
    <tr>
      <td align="center" valign="top" style="border-collapse: collapse;">
        <!-- // Begin Template Preheader \\ -->
        <table border="0" cellpadding="10" cellspacing="0" width="600" id="templatePreheader" style="background-color: #FAFAFA;">
          <tr>
            <td valign="top" class="preheaderContent" style="border-collapse: collapse;">

              <!-- // Begin Module: Standard Preheader \ -->
              <table border="0" cellpadding="10" cellspacing="0" width="100%">
                <tr>
                  <td valign="top" style="border-collapse: collapse;">
                    <div style="color: #505050;font-family: Arial;font-size: 10px;line-height: 100%;text-align: left;"><%= @preview_text %></div>
                  </td>
                  <!--
                  -->
                  <td valign="top" width="190" style="border-collapse: collapse;">
                    <div style="color: #505050;font-family: Arial;font-size: 10px;line-height: 100%;text-align: left;">

                    </div>
                  </td>
                  <!--
                  -->
                </tr>
              </table>
              <!-- // End Module: Standard Preheader \ -->

            </td>
          </tr>
        </table>
        <!-- // End Template Preheader \\ -->
        <table border="0" cellpadding="0" cellspacing="0" width="600" id="templateContainer" style="border: 1px solid #DDDDDD;background-color: #FFFFFF;">
          <tr>
            <td align="center" valign="top" style="border-collapse: collapse;">
              <!-- // Begin Template Header \\ -->
              <table border="0" cellpadding="0" cellspacing="0" width="600" id="templateHeader" style="background-color: #FFFFFF;border-bottom: 0;">
                <tr>
                  <td class="headerContent" style="border-collapse: collapse;color: #202020;font-family: Arial;font-size: 34px;font-weight: bold;line-height: 100%;padding: 0;text-align: center;vertical-align: middle;">

                    <!-- // Begin Module: Standard Header Image \\ -->
                    <div style="text-align: none;"><img src="<%= attachments['email_header.png'].url if attachments['email_header.png'] %>" alt="iFoundAgent.com" border="0" style="border: px none;border-color: ;border-style: none;border-width: px;height: 60px;width: 145px;margin: 0;padding: 0;max-width: 600px;line-height: 100%;outline: none;text-decoration: none;" width="600" height="83" id="headerImage campaign-icon"></div>
                    <!-- // End Module: Standard Header Image \\ -->

                  </td>
                </tr>
              </table>
              <!-- // End Template Header \\ -->
            </td>
          </tr>
          <tr>
            <td align="center" valign="top" style="border-collapse: collapse;">
              <div>
                <table border="0" cellpadding="0" cellspacing="0" width="600" id="templateBody">
                  <tr>
                    <td valign="top" class="bodyContent" style="border-collapse: collapse;background-color: #FFFFFF;">

                      <!-- // Begin Module: Standard Postcard Content \\ -->
                      <table border="0" cellpadding="20" cellspacing="0" width="100%">
                        <tr mc:repeatable="repeat_1" mc:repeatindex="0" mc:hideable="hideable_repeat_1_1" mchideable="hideable_repeat_1_1">
                          <td valign="top" style="border-collapse: collapse;">
                            <div style="color: #505050;font-family: Arial;font-size: 14px;line-height: 150%;text-align: left;">
                              <!-- // Begin Template Body \\ -->
                              <%= yield %>
                              <!-- // End Template Body \\ -->
                              <p>
                                Thanks,<br/>
                                The iFoundAgent.com
                              </p>
                          </td>
                        </tr>
                      </table>
                      <!-- // End Module: Standard Postcard Content \\ -->
                    </td>
                  </tr>
                </table>
              </div>

            </td>
          </tr>
          <tr>
            <td align="center" valign="top" style="border-collapse: collapse;">
              <!-- // Begin Template Footer \\ -->
              <table border="0" cellpadding="10" cellspacing="0" width="600" id="templateFooter" style="background-color: #FFFFFF;border-top: 0;">
                <tr>
                  <td valign="top" class="footerContent" style="border-collapse: collapse;">

                    <!-- // Begin Module: Standard Footer \\ -->
                    <table border="0" cellpadding="10" cellspacing="0" width="100%">
                      <tr>
                        <td colspan="2" valign="middle" id="social" style="border-collapse: collapse;background-color: #eee;border: 0;">
                          <div style="color: #707070;font-family: Arial;font-size: 12px;line-height: 125%;text-align: left;">
                            <em>Copyright &copy; <%= Time.now.year %> iFoundAgent, All rights reserved.</em>
                            <br>

                            You are receiving this email because you registered for iFoundAgent.
                            <br>
                            <strong>Our mailing address is:</strong>
                            <br>
                            <div class="vcard"><span class="org fn">iFoundAgent</span><div class="adr"><div class="street-address">2131 E Broadway Rd</div><div class="extended-address">Ste 28</div><span class="locality">Tempe</span> <span class="region">AZ</span>  <span class="postal-code">85282</span></div></div>

                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="2" valign="middle" id="utility" style="border-collapse: collapse;background-color: #FFFFFF;border: 0;">
                          <div style="color: #707070;font-family: Arial;font-size: 12px;line-height: 125%;text-align: center;">
                            &nbsp;
                            <%
                            # Reminder: Previously I had a link here to allow a user to access their email notification settings. We don't have that functionality yet, so I took it out for now, but this is where it would go, and the HTML would look like the following.
                            # Note that I had to modify the Ruby/ERB script opening/close tags so it doesn't actually run them.
                            #<a href="<= Glimpse::Utils.make_link(notification_settings_path) =>" style="color: #336699;font-weight: normal;text-decoration: underline;">update your email notification settings</a>&nbsp;
                            %>
                          </div>
                        </td>
                      </tr>
                    </table>
                    <!-- // End Module: Standard Footer \\ -->

                  </td>
                </tr>
              </table>
              <!-- // End Template Footer \\ -->
            </td>
          </tr>
        </table>
        <br>
      </td>
    </tr>
  </table>
</center>
</html>
