Feature: Manage videos
  In order to be able to manage videos
  I want to be able to add, edit, list and delete them

  Scenario: <PERSON><PERSON> can manage all videos
    pending

  <PERSON>enario: Client can only manage their own videos
    Given I have two clients and their associated users, each with a video
    Given I am logged in with: email: "<EMAIL>", password: "password"
    When I go to the videos page
    Then I should see "youtube1"
    And I should not see "youtube2"
    # Do UI
    # We should also use RSpec to make sure it can't be done behind the scenes

  @allow-rescue
  Scenario: Videos not shown to guest 
    Given I'm not logged in 
    When I go to the videos page
    Then I should see "need to sign in"

  Scenario: A user can manage videos for multiple clients
    # Reminder: I don't like this at all
    # - too many steps
    # - I didn't have to hard code the numbers, but it's really awkward to get IDs of previously created records. I heard the pickle gem can help.
    # - Using technical stuff like database field names (access_id) defeats
    #   the whole point of using cucumber/gherkin in the first place!
    # However, rather than improve upon this, I might abandon cucumber altogether.
    # When I think of how much it would take to write just to get a simple feature
    # like videos, it seems overwhelming for the client to write all that up.
    # I might just need practice, and I know you can refactor to DRY up steps.
    # But for now I'll just switch to rspec to get things done faster.
    Given a user exists with email: "<EMAIL>", password: "password"
    Given a client exists with access_company: "Client1"
    Given a client exists with access_company: "Client2"
    Given a user_client exists with user_id: 1, access_id: 1
    Given a user_client exists with user_id: 1, access_id: 2
    # Reminder that each video will be created for each client (on the backend,
    # so we recreate that setup here).
    Given a video exists with access_id: 1, youtube_id: "youtube1"
    Given a video exists with access_id: 1, youtube_id: "youtube2"
    Given a video exists with access_id: 2, youtube_id: "youtube1"
    Given a video exists with access_id: 2, youtube_id: "youtube2"
    Given I am logged in with: email: "<EMAIL>", password: "password"
    When I go to the videos page
    Then the number of videos should be 4
    Then I should see "youtube1"
    And I should see "youtube2"

  Scenario: Client can add video
    Given a user exists with email: "<EMAIL>", password: "password"
    Given a client exists with access_company: "Client1"
    Given a user_client exists with user_id: 1, access_id: 1
    Given I am logged in with: email: "<EMAIL>", password: "password"
    When I go to the new video page
    And I fill in video_MLS_id with 777
    And I fill in video_youtube_id with xyz
    And I click on Add video
    Then I should see "success"
    And I should see "777"
    And I should see "xyz"

  Scenario: Client can delete video
    Given a user exists with email: "<EMAIL>", password: "password"
    Given a client exists with access_company: "Client1"
    Given a user_client exists with user_id: 1, access_id: 1
    Given a video exists with access_id: 1, MLS_id: 777, youtube_id: "xyz"
    Given I am logged in with: email: "<EMAIL>", password: "password"
    When I go to the videos page
    And I should see "777"
    And I should see "xyz"
    And in the row containing 777, I click on delete
    Then I should see "success"
    And I should not see "777"
    And I should not see "xyz"

  # Note: This feature is almost just like "Client can add video",
  # except the user has two clients. But from the front-end, there should
  # be no difference because we're hiding that fact to the user.
  Scenario: When client adds video, it's added to each of client's sites
    Given a user exists with email: "<EMAIL>", password: "password"
    And a client exists with access_company: "Client1"
    And a client exists with access_company: "Client2"
    And a user_client exists with user_id: 1, access_id: 1
    And a user_client exists with user_id: 1, access_id: 2
    And I am logged in with: email: "<EMAIL>", password: "password"
    When I go to the new video page
    And I fill in video_MLS_id with 777
    And I fill in video_youtube_id with xyz
    And I click on Add video
    Then I should see "success"
    And I should see "777"
    And I should see "xyz"
    And the count of table rows should be 1

  @focus
  Scenario: When user with multiple clients deletes a video, it's deleted from each of client's sites
    Given a user exists with email: "<EMAIL>", password: "password"
    Given a client exists with access_company: "Client1"
    Given a client exists with access_company: "Client2"
    Given a user_client exists with user_id: 1, access_id: 1
    Given a user_client exists with user_id: 1, access_id: 2
    Given a video exists with access_id: 1, MLS_id: 777, youtube_id: "youtube1"
    Given a video exists with access_id: 2, MLS_id: 777, youtube_id: "youtube1"
    Given I am logged in with: email: "<EMAIL>", password: "password"
    When I go to the videos page
    And I should see "777"
    And in the row containing 777, I click on delete
    Then I should see "success"
    And I should not see "777"
    And I should not see "youtube1"
    # Then show me the page
    Then the number of videos should be 0
