@echo off
echo 🛑 Stopping Pro-Found IDX System...
cd /d "%~dp0"

echo 📋 Current running containers:
docker-compose -f docker-compose.simple.yml ps

echo.
echo 🛑 Stopping all containers...
docker-compose -f docker-compose.simple.yml down

echo ⏳ Waiting for containers to stop...
timeout /t 3 /nobreak > nul

echo.
echo ✅ Pro-Found IDX System Stopped!
echo.
echo 📋 To start again:
echo   - Full start: start-profoundidx.bat
echo   - Quick restart: quick-restart-profoundidx.bat
echo.
pause
