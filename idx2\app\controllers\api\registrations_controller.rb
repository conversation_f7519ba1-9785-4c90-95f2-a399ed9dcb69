module Api
  class RegistrationsController < ApplicationController
    before_filter :authenticate_custom_admin_user!
    respond_to :json

    def show
      respond_with Registration.find(params['id'])
    end

    def build
      registration = Registration.find(params['id'])
      registration.website_state = "not_started"
      registration.save!
      NewWebsiteWorker.perform_async(params['id'], "sigilpreternatural")
      render json: { message: "ok" }
    end

    def reset
      registration = Registration.find(params['id'])
      registration.website_state = "not_started"
      registration.save!
      render json: { message: "ok" }
    end
  end
end
