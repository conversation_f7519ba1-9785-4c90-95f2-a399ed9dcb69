class ProductionTestsController < ApplicationController
  before_filter :authenticate_custom_admin_user!

  def index
    @title = "Production Tests"
  end

  def email_test
    @title = "Email Test"

    if params[:email] && params[:email][:send_email]
      hash = { mykey: "myvalue" }
      ProductionTestsMailer.delay.email_test(hash)
      mail = ProductionTestsMailer.email_test(hash)
      flash.now[:success] = "Added email (to #{mail.to.first}) to Sidekiq queue"
    end
  end
end
