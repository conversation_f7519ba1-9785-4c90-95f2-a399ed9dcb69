.reseller_show_page

  h1= @reseller.display_name

  a(href=resellers_path)
    | &laquo; Return to resellers list

  p= link_to "Edit", edit_reseller_path(@reseller), class: "btn btn-primary btn-large edit-button-padding"

  .name-value-pairs
    dl
      dt.form_label Friendly ID
      dd.form_label= nbsp(@reseller.friendlyid)
    dl
      dt.form_label Display name
      dd.form_label= nbsp(@reseller.display_name)
    dl
      dt.form_label Business name
      dd.form_label= nbsp(@reseller.business_name)
    dl
      dt.form_label Admin email
      dd.form_label= nbsp(@reseller.admin_email)
    dl
      dt.form_label Alternative support email
      dd.form_label= nbsp(@reseller.support_email)
    dl
      dt.form_label Destination multisite URL
      dd.form_label= nbsp(@reseller.website_server)
    dl
      dt.form_label Signup URL
      dd.form_label= nbsp(@reseller.signup_url)
    dl
      dt.form_label Payment URL
      dd.form_label= nbsp(@reseller.payment_url)
    dl
      dt.form_label Website URL
      dd.form_label= nbsp(@reseller.website_url)
    dl
      dt.form_label Theme previews URL
      dd.form_label= nbsp(@reseller.theme_previews_url)
    dl
      dt.form_label Sliders URL
      dd.form_label= nbsp(@reseller.sliders_url)
    dl
      dt.form_label Monthly rate
      dd.form_label= nbsp(@reseller.monthly_rate)
    dl
      dt.form_label Recurring method ID
      dd.form_label= nbsp(@reseller.recurring_method_id)

  div
    h3 Signup page code
    p When building the Signup page, you can use the following code:

    strong Short form

    p We call this the 'short' form.

    pre= signup_page_code(@reseller.friendlyid)

    strong Long form

    p
      | The long form is the same, except replace the iframe src's
      code<> new_idx_access_only
      | to
      code< new
      | .

    strong Pay page

    p Here's the pay page:

    pre= pay_page_code @reseller.friendlyid

    quote
      | If you don't have a promo code, leave off the part that says
      code<>
        / Reminder, unfortunately, my use here of the ampersand on the end depends on the names of the parameters in the URL. Rails automatically orders them alphabetically. So it works for now, but could be confusing later if the param order changes.
        | promo_code=PROMO_CODE_HERE&

  p
    a(href=resellers_path)
      | &laquo; Return to resellers list

  p &nbsp;

h2 Pricing

= link_to 'Reseller pricing', [@reseller, :reseller_pricings]

br
br
