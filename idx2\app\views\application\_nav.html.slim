.container
  .navbar
    .navbar-inner
      a.brand(href=root_path)
        = image_tag "ifoundagent_logo.png"
        | &nbsp;iFoundAgent Admin
      ul.nav
        -if user_signed_in?
          - if can? :manage, Client
            li
              a(href=clients_path) Clients
          - if can? :resellers, Reseller
            li
              a(href=resellers_path) Resellers
          - if can? :manage, MlsSystem
            li
              = link_to "MLS Systems", mls_systems_path
      ul.nav.pull-right
        - if user_signed_in?
          li
            = link_to current_user.email, "#"
          li
            = link_to "Sign out", destroy_user_session_url(protocol: "http"), method: :delete
        - else
          li
            = link_to "Register", new_client_path
          li
            = link_to "Sign in", user_session_path
