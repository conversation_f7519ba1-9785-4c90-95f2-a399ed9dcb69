.client_edit_page

  h1= "Edit client #{@client.access_company}"

  = simple_form_for @client, html: { multipart: true, class: "form-horizontal" }, defaults: { input_html: { class: "edit_field" } } do |f|
    .client
      .contact_information
        h3 Contact Information
        = f.input :access_company
        = f.input :access_fullname
        = f.input :access_address
        = f.input :access_address2
        = f.input :access_city
        = f.input :access_state
        = f.input :access_zipcode
        = f.input :access_phone
        = f.input :access_emailaddress
      .account_information
        h3 Account information
        = f.input :access_status, as: :boolean, input_html: { class: "auto-width" }
        = f.input :access_account_id
        = f.input :access_apikey
        = f.input :mls
        = f.input :access_member_id
        = f.input :access_office_id
        = f.input :broker_office_name
        = f.input :access_youtube_id
        = f.input :hide_ucb, input_html: { class: "auto-width" }
        = f.input :domain
        = f.input :ftp_addr
        = f.input :ftp_user
        = f.input :ftp_pass
        = f.input :URLs, input_html: { class: "edit_field tall" }
        = f.association :reseller, label_method: :display_name
    .registration
      h3 Registration Information
      - if @client.registration
        = f.simple_fields_for :registration do |reg_form|
          = reg_form.input :id, as: :hidden
          = reg_form.input :theme
          = reg_form.input :market_area
          = reg_form.input :existing_website_url
          = reg_form.input :facebook_url
          = reg_form.input :linkedin_url
          = reg_form.input :twitter_url
          = reg_form.input :google_plus_url
          = reg_form.input :youtube_url
          = reg_form.input :other_social_media
          = reg_form.input :logo
          = reg_form.input :remove_logo, as: :boolean, input_html: { class: "auto-width" }
          = reg_form.input :headshot
          = reg_form.input :remove_headshot, as: :boolean, input_html: { class: "auto-width" }
          = reg_form.input :color_scheme
          = reg_form.input :title
          = reg_form.input :tagline
          = reg_form.input :promo_code
          = reg_form.input :notes
          = reg_form.input :epn_response
          = reg_form.input :website_state
          = reg_form.input :website_request
          = reg_form.input :website_response
          = reg_form.input :payment_state
      - else
        p.muted= "[No registration]"
    .access_meta
      h3 SEO Data
      .alert.alert-warning Reminder: these don't matter any more. Only the templates from the MLS system are used.
      = f.simple_fields_for :access_meta do |access_meta_form|
        = access_meta_form.input :id, as: :hidden
        .category
          h4 Category
          = access_meta_form.input :meta_cat_title
          = access_meta_form.input :meta_cat_h1
          = access_meta_form.input :meta_cat_description, input_html: { class: "edit_field tall" }
          = access_meta_form.input :meta_cat_keywords, input_html: { class: "edit_field tall" }
        .search_results
          h4 Search results
          = access_meta_form.input :meta_result_title
          = access_meta_form.input :meta_result_h1
          = access_meta_form.input :meta_result_description, input_html: { class: "edit_field tall" }
          = access_meta_form.input :meta_result_prop_h2
          = access_meta_form.input :meta_result_prop_content, input_html: { class: "edit_field tall" }
        .property
          h4 Property
          = access_meta_form.input :meta_prop_url
          = access_meta_form.input :meta_prop_title
          = access_meta_form.input :meta_prop_h1
          = access_meta_form.input :meta_prop_keywords, input_html: { class: "edit_field tall" }
          = access_meta_form.input :meta_prop_description, input_html: { class: "edit_field tall" }
      h3 Templates
      .alert.alert-warning Reminder: these don't matter any more. Only the templates from the MLS system are used.
        With the exception of the disclosure, which comes from the ifoundadmin site, as mentioned below.
      = f.simple_fields_for :access_meta do |access_meta_form|
        = access_meta_form.input :id, as: :hidden
        = access_meta_form.input :meta_disclosure, hint: 'Reminder that this discloure value is ignored, as it\'s now controlled by ifoundadmin.com, see e.g. https://mls-associations.ifoundadmin.com/wp-admin/post.php?post=242&action=edit'
        = access_meta_form.input :meta_address
        = access_meta_form.input :meta_street_address
        h4 Property Display Page
        .property-display-page
          h5 PDP Main Content
          .property_description
            = access_meta_form.input :meta_pdp_main_label
            = access_meta_form.input :meta_pdp_main_value, input_html: { class: "edit_field tall" }
          h5 Property Description
          .property_description
            = access_meta_form.input :meta_pdp_property_label
            = access_meta_form.input :meta_pdp_property_value, input_html: { class: "edit_field tall" }
          h5 General Information
          .general_information
            = access_meta_form.input :meta_pdp_general_label
            = access_meta_form.input :meta_pdp_general_value, input_html: { class: "edit_field tall" }
          h5 School Information
          .school_information
            = access_meta_form.input :meta_pdp_school_label
            = access_meta_form.input :meta_pdp_school_value, input_html: { class: "edit_field tall" }
          h5 Community Information
          .community_information
            = access_meta_form.input :meta_pdp_community_label
            = access_meta_form.input :meta_pdp_community_value, input_html: { class: "edit_field tall" }
          h5 Lot Information
          .lot_information
            = access_meta_form.input :meta_pdp_lot_label
            = access_meta_form.input :meta_pdp_lot_value, input_html: { class: "edit_field tall" }
          h5 Rooms Information
          .rooms_information
            = access_meta_form.input :meta_pdp_rooms_label
            = access_meta_form.input :meta_pdp_rooms_value, input_html: { class: "edit_field tall" }
          h5 Location Information
          .location_information
            = access_meta_form.input :meta_pdp_location_label
            = access_meta_form.input :meta_pdp_location_value, input_html: { class: "edit_field tall" }
          h5 Property Features
          .location_information
            = access_meta_form.input :meta_pdp_features_label
            = access_meta_form.input :meta_pdp_features_value, input_html: { class: "edit_field tall" }
      .clearfix
    .features
      h3 Features
      = f.input :feature_gui, as: :boolean, input_html: { class: "auto-width" }
    = f.submit "Save client data", class: "btn-primary"
    | &nbsp;
    = link_to "Cancel edit", client_path(@client), class: "btn"
