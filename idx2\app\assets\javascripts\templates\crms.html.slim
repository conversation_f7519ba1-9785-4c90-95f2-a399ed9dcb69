h3 CRMs

div cg-busy="{promise:myPromise,message:'Loading Your Data'}"

p For instructions on adding a CRM connection, see below for the particular CRM provider.

.well

  .add
    form.form-inline
      p Add a CRM connection:
      select ng-model="form.computerName"
        - for option in Api::Crm::CrmApi.available_apis
          option value=option.to_s = option.display_name
      input<> type='text' ng-model='form.apikey' placeholder='API key'
      button.btn.btn-primary ng-click="add()"
        i.icon-plus-sign.icon-white>
        | Add

  p Existing CRM connections:

  .existing
    table.table.table-striped.ng-table-responsive ng-table="tableParams" ng-show="list.length > 0"
      tr ng-repeat="crm in list"
        td data-title="'Name'" {{crm.display_name}}
        td data-title="'API key'" {{crm.apikey}}
        td data-title="'Enabled'" {{crm.enabled}}
        / td data-title="'Primary'" {{crm.primary}}
        td data-title="'Actions'"
          button.btn.btn-danger.btn-small ng-click="delete(crm)"
            i.icon-remove.icon-white>
            | Remove
    div ng-show="list.length == 0"
      em No connections yet!

.crm-note
  h4 LionDesk
  blockquote This should work with Mac. I haven't tested it with Windows.
  p To add a Wise Agent CRM connection, you'll need the API key. To get it, use the following command line, replacing [EMAIL ADDRESS] with the email address that the client uses to log into LionDesk.
  / As a reminder to self, the command line here uses --silent to not show the download progress. We use 2>&1 to redirect stderr to stdout so we can silence it.
  pre curl --silent -X POST -H "Authorization: Basic ZDQ2MjFkNzVkOTQyYmMzYWM1YmUwNDk1MWQ0ZDE0Y2M6" -H "Content-Type: application/json" -H "Cache-Control: no-cache" -d '{"action":"GetUsers"}' 'https://api-v1.liondesk.com' 2>&1 | python -m json.tool | grep -A 1 [EMAIL ADDRESS]
  p You'll get back an answer like
  pre "email": "<EMAIL>",
    "userKey": "abc123"
  p Copy what's in the quotes (it will be about 32 characters) from the "userKey" as the API key.

.crm-note
  h4 Wise Agent
  p To add a Wise Agent CRM connection, you'll need the API key. To get it, use the following command line, replacing [USERNAME] with the Wise Agent username from the client/agent.
  pre curl -X POST -H "Cache-Control: no-cache" -H "Content-Type: application/x-www-form-urlencoded" -d 'requestType=userid&username=[USERNAME]' https://sync.thewiseagent.com/http/webconnect.asp
  p You'll get back an answer like
  pre 12,345,678,90,12,123,45,67,890
  p which you should use as the API key.
