require 'spec_helper'

module Api
  describe FieldMappingsController do
    before(:each) do
      request.env['HTTPS'] = 'on'
      sign_in admin: true
    end

    it "can create a field mapping" do
      expect(FieldMapping.count).to eq(0)
      field_mapping = FactoryGirl.build_stubbed :field_mapping
      data = {
        mls_class: 'rentals',
        MapName: 'Bedrooms',
      }
      params = data.merge({ format: :json })
      post :create, params
      expect(FieldMapping.count).to eq(1)
      created_field_mapping = FieldMapping.first
      expect(created_field_mapping.mls_class).to eq('rentals')
      expect(created_field_mapping.MapName).to eq('Bedrooms')
    end

    it "can update a field mapping" do
      field_mapping = FactoryGirl.create :field_mapping, mls_class: 'rentals', MapName: 'Bedrooms', armls: 'starting value'
      data = {
        mls_class: field_mapping.mls_class,
        MapName: field_mapping.MapName,
        armls: 'changed value'
      }
      params = data.merge({ format: :json })
      patch :update, params
      expect(FieldMapping.count).to eq(1)
      created_field_mapping = FieldMapping.first
      expect(created_field_mapping.armls).to eq('changed value')
    end
  end
end
