SRV = <EMAIL>
SSH = ssh $(SRV)
LIVE_DIR = /home/<USER>/public_html/get/ 
GIT_REPO = /home/<USER>/src/profoundidx
EXCLUDE = --exclude=.* --exclude=dbconfig.ini
MAKEFLAGS=--no-print-directory

web:
	php -S localhost:8344

# Run a command on the server in the live directory for the IDX
live-cmd:
	$(SSH) "cd $(LIVE_DIR) && $(CMD)"

#====================================================================
# Pushing changes

# Update the live copy with the latest from git
update-live:
	make live-cmd CMD="git pull"

# Push local commits to the server and update the live copy
push:
	git push
	make update-live

#====================================================================
# Debugging targets

# Push local changes to the live directory without commiting to git
sync:
	rsync -avz $(EXCLUDE) . $(SRV):$(LIVE_DIR)

# Reset the live directory to the latest git commit
live-reset:
	make live-cmd CMD="git reset --hard"

fixup:
	$(SSH) "cd $(GIT_REPO) && git update-ref HEAD HEAD^ && cd $(LIVE_DIR) && git reset --hard HEAD^"


#====================================================================
# .htaccess file

# TODO: grab include path from php -i or other, and shove it in to template

LIMIT = 100
MLS = armls
MLS_CLASS = A

MLS_CNAME = res
CACHE = false
SCHEMA_ONLY = false
LOOKUPS=1
DAYSAGO=1

rets-sync:
	@$(RM) -f ./rets.$(MLS).$(MLS_CLASS).lock
	php includes/classes/rets.php --mls $(MLS) --class $(MLS_CLASS) --cmd props --images 1 --limit $(LIMIT) --cache $(CACHE) --schema_only $(SCHEMA_ONLY) --daysago $(DAYSAGO)

rets-setup-%:
	@$(MAKE) rets-sync MLS=$* SCHEMA_ONLY=true
	php includes/classes/rets.php --mls $* --class $(MLS_CLASS) --cmd fields --lookups $(LOOKUPS)

# Allow usage of `make rets-sync-armls` syntax
rets-sync-%:
	@$(MAKE) rets-sync MLS=$*
