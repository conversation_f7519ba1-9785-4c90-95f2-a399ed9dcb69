class NewWebsiteApi
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def build(registration, password)
    # TODO: We should be using the grape gem (or roar/active-model-
    # serializers/jbuilder/rabl/etc) to do this. I'm keeping it like this for
    # now for simplicity, but after we know it works is when we should use some
    # gem.
    client = registration.client
    if client.reseller_id
      self.class.base_uri client.reseller.website_server
    else
      self.class.base_uri Rails.configuration.wpbuilder.base_uri
    end
    client_node = {
      access_fullname: client.name,
      access_company: client.company,
      access_emailaddress: client.email,
      access_phone: ActiveSupport::NumberHelper.number_to_phone(client.phone, area_code: true),
      broker_office_name: client.broker_office_name,
      access_account_id: client.account_id,
      access_apikey: client.apikey,
      mls: client.mls,
      access_address: client.address,
      access_zipcode: client.zipcode
    }
    registration_node = {
      theme: registration.theme,
      # lowercase, add dashes and such: http://stackoverflow.com/a/4309257/135101
      color: registration.color_scheme.parameterize,
      title: registration.title,
      tagline: registration.tagline,
      facebook_url: registration.facebook_url,
      linkedin_url: registration.linkedin_url,
      twitter_url: registration.twitter_url,
      google_plus_url: registration.google_plus_url,
      youtube_url: registration.youtube_url
    }
    user = registration.client.users.first
    user_node = { plaintext_password: password }
    payload = {
      wp_builder: {
        client: client_node,
        registration: registration_node,
        user: user_node
      }
    }
    payload_json = payload.to_json
    body = "payload=#{CGI.escape payload_json}"
    response = post query: { action: "build" }, body: body
    yield body, response if block_given?
    response
  end

  def get(params)
    self.class.get route, params
  end

  def post(params)
    self.class.post route, params
  end

  def route
    '/wp-admin/admin-ajax.php'
  end
end
