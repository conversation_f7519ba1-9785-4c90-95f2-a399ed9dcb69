= simple_form_for(@reseller) do |f|
  = f.error_notification

  div.form-inputs
    = f.input :friendlyid
    = f.input :display_name, hint: "What website users will actually see"
    = f.input :business_name, hint: "Any name you want. Only for records keeping."
    = f.input :admin_email
    = f.input :support_email, hint: "Alternate email for support. To be used only by Free sites. Leave empty if the default email for iFoundAgent should be used"
    = f.input :website_server, hint: "Where the website will be built, e.g. http://www.getprofoundidx.com"
    = f.input :signup_url, hint: "This is just for records keeping, reminding you of where the page is"
    = f.input :payment_url
    = f.input :website_url, hint: "Main website URL, e.g. http://www.ifoundagent.com"
    = f.input :theme_previews_url
    = f.input :sliders_url
    = f.input :monthly_rate
    = f.input :recurring_method_id, hint: "This is set up on the eProcessing Network site and determines the order total, overriding the price you set here!"

  div.form-actions
    = f.button :submit
