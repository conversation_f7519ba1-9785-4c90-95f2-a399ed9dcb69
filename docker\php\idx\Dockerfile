# Reminder how to build this. Change directory to this dir, then do:
# docker image build -t ifoundagent:idx-bullseye .
# We could use docker's php fpm base image, but to try to keep parity with our host, let's use our Debian base.
FROM ifoundagent:base

# We need Sury APT repository for Debian
# See: https://computingforgeeks.com/how-to-install-php-on-debian-linux-2/
RUN wget -q https://packages.sury.org/php/apt.gpg -O- | apt-key add -
RUN echo "deb https://packages.sury.org/php/ bullseye main" > /etc/apt/sources.list.d/php.list

RUN apt-get update

RUN apt-get -y --no-install-recommends --fix-missing install \
        default-mysql-client \
        php8.1 \
        php8.1-fpm \
        php8.1-common \
        php8.1-curl \
        php8.1-mbstring \
        php8.1-mysql \
        php8.1-xml \
        php8.1-zip \
        php8.1-readline \
        php8.1-dev \
        lsof \
        # Provides netstat
        net-tools \
        # Needed to build (configure) solr php extension
        pkg-config \
        libcurl4-openssl-dev \
        libxml2 \
        libxml2-dev

RUN apt-get install php-pear

# Curl libraries have moved with Debian stretch, but PEAR expects them here
RUN ln -s /usr/include/x86_64-linux-gnu/curl /usr/include/curl

# Update PEAR and install the DB module
RUN pear channel-update pear.php.net
RUN pear install db

WORKDIR /root
RUN wget -O solr-2.6.0.tgz https://pecl.php.net/get/solr-2.6.0 && tar -xvzf solr-2.6.0.tgz
WORKDIR /root/solr-2.6.0
RUN phpize && aclocal && autoreconf && ./configure && make && make install
WORKDIR /

# Install Composer
RUN wget -O composer-setup.php https://getcomposer.org/installer
RUN php composer-setup.php --install-dir=/
RUN rm composer-setup.php

RUN pecl install xdebug-3.1.6

# Directorys & permissions
RUN mkdir -p /var/www/.composer/cache/
RUN mkdir -p /www/vendor
RUN chown www-data:www-data /var/www/.composer/cache/ /www/vendor

# Declare directories to be used as volumes
VOLUME /var/www/.composer/cache/
VOLUME /www/vendor

# Make log directory writeable to PHP-FPM user
RUN mkdir -p /run/php

# Configure FPM to run properly on docker
RUN mkdir -p /var/log/php-fpm/

EXPOSE 9000

RUN chown www-data:www-data /www

WORKDIR /www

RUN curl -o /bin/wp https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar && chmod +x /bin/wp

# Run PHP-FPM. Must use -F so it doesn't daemonize (which would stop the container).
CMD ["/usr/sbin/php-fpm8.1", "-F"]
