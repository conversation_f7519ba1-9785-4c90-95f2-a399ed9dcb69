passwords = YAML.load_file "#{File.dirname(__FILE__)}/passwords.yml"

admins = %w{<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>}
admins.each do |email|
  unless User.where(email: email).first
    User.create!(email: email, password: passwords['users'][email], admin: true)
  end
end

# At first I'm just creating this list by hand as we ask clients with a manual process to try out the video system.
clients =
  [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ]
clients.each do |email|
  unless User.where(email: email).first
    User.create!(email: email, password: passwords['users'][email])
  end
end
