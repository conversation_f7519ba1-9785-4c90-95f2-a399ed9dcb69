class MlsSystemsController < ApplicationController
  before_filter :authenticate_custom_admin_user!

  def index
    @mls_systems = MlsSystem.all
  end

  def show
    @mls_system = MlsSystem.where(name: show_params).first
  end

  def edit
    @mls_system = MlsSystem.where(name: show_params).first
  end

  def update
    commit = params[:commit]
    if commit.starts_with?("Copy SEO defaults")
      @mls_system = MlsSystem.where(name: params[:id]).first
      @mls_system.copy_to_clients
      redirect_to mls_system_path(@mls_system.name), notice: "#{@mls_system.display_name}'s SEO defaults were successfully copied"
    elsif commit.starts_with?("Copy template defaults")
      @mls_system = MlsSystem.where(name: params[:id]).first
      @mls_system.copy_template_to_clients
      redirect_to mls_system_path(@mls_system.name), notice: "#{@mls_system.display_name}'s template defaults were successfully copied"
    else
      @mls_system = MlsSystem.where(name: show_params).first
      if @mls_system.update_attributes(update_params)
        redirect_to mls_system_path(@mls_system.name), notice: "#{@mls_system.display_name} was successfully updated"
      else
        render :edit
      end
    end
  end

  def show_params
    params.require(:id)
  end

  def update_params
    params.require(:mls_system).permit [
      :meta_cat_title,
      :meta_cat_h1,
      :meta_cat_description,
      :meta_cat_keywords,
      :meta_result_title,
      :meta_result_h1,
      :meta_result_description,
      :meta_result_prop_h2,
      :meta_result_prop_content,
      :meta_prop_url,
      :meta_prop_title,
      :meta_prop_h1,
      :meta_prop_description,
      :meta_prop_keywords,
      :meta_disclosure,
      :meta_address,
      :meta_street_address,
      :meta_pdp_main_label,
      :meta_pdp_main_value,
      :meta_pdp_property_label,
      :meta_pdp_property_value,
      :meta_pdp_general_label,
      :meta_pdp_general_value,
      :meta_pdp_school_label,
      :meta_pdp_school_value,
      :meta_pdp_community_label,
      :meta_pdp_community_value,
      :meta_pdp_lot_label,
      :meta_pdp_lot_value,
      :meta_pdp_rooms_label,
      :meta_pdp_rooms_value,
      :meta_pdp_location_label,
      :meta_pdp_location_value,
      :meta_pdp_features_label,
      :meta_pdp_features_value
    ] 
  end
end
