MlsSystem.find_or_create_by(name: "armls") do |mls|
  mls.display_name = "ARMLS"
  mls.meta_prop_url = "{StreetNumber}-{StreetDirPrefix}-{StreetName}-{StreetSuffix}-{City}-{PostalCode}"
  mls.meta_prop_title = "{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {City} {PostalCode}"
  mls.meta_prop_h1 = "{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {City} {PostalCode}"
  mls.meta_prop_description = "{City} Home for sale, {Remarks}"
  mls.meta_prop_keywords = ""
  mls.meta_prop_description = ""
  mls.meta_result_title = ""
  mls.meta_result_h1 = ""
  mls.meta_result_keywords = ""
  mls.meta_result_description = "Complete list of ARMLS homes for sale"
  mls.meta_result_prop_h2 = "{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber} {City} {PostalCode}, {Subdivision}"
  mls.meta_result_prop_content = "<strong>{ListPrice}<br />Bedrooms: {Beds}, Baths: {Bathrooms}<br />Home size: {SquareFeet} sqft<br />Lot size: {LotSquareFeet} sqft<br />Yr Built: {YearBuilt}<br />MLS #: {ListingID}"
  mls.meta_cat_title = "{category} in {City}"
  mls.meta_cat_h1 = "{category} in {City}"
  mls.meta_cat_keywords = ""
  mls.meta_cat_description = "{category} in {City}"
  mls.meta_geoapi = ""
  mls.meta_links_seo = ""
  mls.meta_disclosure = ""
  mls.meta_address = ""
  mls.meta_street_address = ""
  mls.meta_pdp_property_label = ""
  mls.meta_pdp_property_value = ""
  mls.meta_pdp_school_label = ""
  mls.meta_pdp_school_value = ""
  mls.meta_pdp_community_label = ""
  mls.meta_pdp_community_value = ""
  mls.meta_pdp_lot_label = ""
  mls.meta_pdp_lot_value = ""
  mls.meta_pdp_rooms_label = ""
  mls.meta_pdp_rooms_value = ""
  mls.meta_pdp_location_label = ""
  mls.meta_pdp_location_value = ""
end

MlsSystem.find_or_create_by(name: "trendmls") do |mls|
  mls.display_name = "TREND"
  mls.meta_prop_url = "{StreetNumber}-{StreetName}-{StreetSuffix}-{City}-{ListingArea}"
  mls.meta_prop_title = "{StreetNumber} {StreetName} {StreetSuffix} {City} {ListingArea} {ListingID}"
  mls.meta_prop_h1 = "{StreetNumber} {StreetName} {StreetSuffix} {City} {ListingArea} {ListingID}"
  mls.meta_prop_description = "{StreetNumber} {StreetName} {StreetSuffix} {City} - {Remarks} Home for Sale "
  mls.meta_prop_keywords = ""
  mls.meta_result_title = ""
  mls.meta_result_h1 = ""
  mls.meta_result_keywords = ""
  mls.meta_result_description = ""
  mls.meta_result_prop_h2 = "{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber} {City} {PostalCode}, {Subdivision}"
  mls.meta_result_prop_content = "<strong>{ListPrice}<br />Bedrooms: {Beds}, Baths: {BathsFull}<br />Home size: {SquareFeet} sqft<br />Property Age: {PropertyAge}<br />MLS #: {ListingID}<br />Listing courtesy of: {ListOfficeName}</strong>"
  mls.meta_cat_title = ""
  mls.meta_cat_h1 = ""
  mls.meta_cat_keywords = "{category} in {City}"
  mls.meta_cat_description = ""
  mls.meta_geoapi = ""
  mls.meta_links_seo = ""
  mls.meta_disclosure = ""
  mls.meta_address = ""
  mls.meta_street_address = ""
  mls.meta_pdp_property_label = ""
  mls.meta_pdp_property_value = ""
  mls.meta_pdp_general_label = ""
  mls.meta_pdp_general_value = ""
  mls.meta_pdp_school_label = ""
  mls.meta_pdp_school_value = ""
  mls.meta_pdp_community_label = ""
  mls.meta_pdp_community_value = ""
  mls.meta_pdp_lot_label = ""
  mls.meta_pdp_lot_value = ""
  mls.meta_pdp_rooms_label = ""
  mls.meta_pdp_rooms_value = ""
  mls.meta_pdp_location_label = ""
  mls.meta_pdp_location_value = ""
end

MlsSystem.find_or_create_by(name: "sndmls") do |mls|
  mls.display_name = "Sandicor"
  mls.meta_prop_url = "{StreetNumber}-{StreetName}-{StreetSuffix}-{City}-{ListingArea}"
  mls.meta_prop_title = "{StreetNumber} {StreetName} {StreetSuffix} {City} {ListingArea} {ListingID}"
  mls.meta_prop_h1 = "{StreetNumber} {StreetName} {StreetSuffix} {City} {ListingArea} {ListingID}"
  mls.meta_prop_description = "{StreetNumber} {StreetName} {StreetSuffix} {City} - {Remarks} Home for Sale "
  mls.meta_prop_keywords = ""
  mls.meta_result_title = ""
  mls.meta_result_h1 = ""
  mls.meta_result_keywords = ""
  mls.meta_result_description = ""
  mls.meta_result_prop_h2 = "{StreetNumber} {StreetDirPrefix} {StreetName} {StreetSuffix} {UnitNumber} {City} {PostalCode}, {Subdivision}"
  mls.meta_result_prop_content = "<strong>{ListPrice}<br />Bedrooms: {Beds}, Baths: {BathsFull}<br />Home size: {SquareFeet} sqft<br />Property Age: {PropertyAge}<br />MLS #: {ListingID}<br />Listing courtesy of: {ListOfficeName}</strong>"
  mls.meta_cat_title = ""
  mls.meta_cat_h1 = ""
  mls.meta_cat_keywords = "{category} in {City}"
  mls.meta_cat_description = ""
  mls.meta_geoapi = ""
  mls.meta_links_seo = ""
  mls.meta_disclosure = ""
  mls.meta_address = ""
  mls.meta_street_address = ""
  mls.meta_pdp_property_label = ""
  mls.meta_pdp_property_value = ""
  mls.meta_pdp_general_label = ""
  mls.meta_pdp_general_value = ""
  mls.meta_pdp_school_label = ""
  mls.meta_pdp_school_value = ""
  mls.meta_pdp_community_label = ""
  mls.meta_pdp_community_value = ""
  mls.meta_pdp_lot_label = ""
  mls.meta_pdp_lot_value = ""
  mls.meta_pdp_rooms_label = ""
  mls.meta_pdp_rooms_value = ""
  mls.meta_pdp_location_label = ""
  mls.meta_pdp_location_value = ""
end
