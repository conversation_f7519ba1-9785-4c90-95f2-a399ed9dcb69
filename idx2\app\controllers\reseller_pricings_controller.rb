class ResellerPricingsController < ApplicationController
  before_filter :authenticate_user!

  def index
    @reseller = Reseller.find params[:reseller_id]
    @reseller_pricings = @reseller.reseller_pricings
  end

  def new
    @reseller = Reseller.find params[:reseller_id]
    @reseller_pricing = ResellerPricing.new
  end

  def show
    @reseller = Reseller.find params[:reseller_id]
    @reseller_pricing = ResellerPricing.friendly.find params[:id]
  end

  def edit
    @reseller = Reseller.find params[:reseller_id]
    @reseller_pricing = ResellerPricing.friendly.find params[:id]
  end

  def create
    @reseller = Reseller.find params[:reseller_id]
    @reseller_pricing = @reseller.reseller_pricings.build reseller_pricing_params

    if @reseller_pricing.save
      redirect_to [@reseller, @reseller_pricing], notice: 'Reseller pricing was successfully created.'
    else
      render :new
    end
  end

  def update
    @reseller = Reseller.find params[:reseller_id]
    @reseller_pricing = ResellerPricing.friendly.find params[:id]

    if @reseller_pricing.update reseller_pricing_params
      redirect_to [@reseller, @reseller_pricing], notice: 'Reseller pricing was successfully created.'
    else
      render :edit
    end
  end

  def destroy
    @reseller = Reseller.find params[:reseller_id]
    @reseller_pricing = ResellerPricing.friendly.find params[:id]
    @reseller_pricing.destroy
    redirect_to [@reseller, :reseller_pricings], notice: 'Reseller was successfully destroyed.'
  end

protected

  def reseller_pricing_params
    params.required(:reseller_pricing).permit(
      :promo_code,
      :theme,
      :recurring_method_id,
      :monthly_rate,
      :payment_url
    )
  end
end
