# See: https://stackoverflow.com/a/74198783/135101
server {
    client_header_buffer_size 5120k;
    large_client_header_buffers 16 5120k;
}

# Default server for localhost
server {
    listen 80 default_server;
    server_name localhost _;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Default location - show available services
    location / {
        return 200 '<!DOCTYPE html>
<html>
<head>
    <title>Pro-Found IDX Development Environment</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #007cba; padding-bottom: 10px; }
        h2 { color: #007cba; margin-top: 30px; }
        .service { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
        .service h3 { margin: 0 0 10px 0; color: #333; }
        .service a { color: #007cba; text-decoration: none; font-weight: bold; }
        .service a:hover { text-decoration: underline; }
        .status { color: #28a745; font-weight: bold; }
        .description { color: #666; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 Pro-Found IDX Development Environment</h1>
        <p>Welcome to your local development environment. All services are running and accessible.</p>

        <h2>🚀 Available Services</h2>

        <div class="service">
            <h3>IDX Server API <span class="status">✅ Running</span></h3>
            <div class="description">Node.js API server for IDX operations</div>
            <a href="http://localhost:8155" target="_blank">http://localhost:8155</a> |
            <a href="http://localhost:8155/health" target="_blank">Health Check</a> |
            <a href="http://localhost:8155/api/test" target="_blank">API Test</a>
        </div>

        <div class="service">
            <h3>Solr Search Engine <span class="status">✅ Running</span></h3>
            <div class="description">Apache Solr for property search and indexing</div>
            <a href="http://localhost:8983" target="_blank">http://localhost:8983</a> |
            <a href="http://localhost:8983/solr/" target="_blank">Admin Interface</a>
        </div>

        <div class="service">
            <h3>MySQL Databases <span class="status">✅ Running</span></h3>
            <div class="description">Database services for IDX and WordPress</div>
            IDX Database: <code>localhost:33066</code><br>
            WordPress Database: <code>localhost:33067</code>
        </div>

        <h2>🔧 Development URLs</h2>
        <p>Add these to your <code>hosts</code> file for domain-based development:</p>
        <div class="service">
            <code>127.0.0.1 idx.test</code><br>
            <code>127.0.0.1 idx-server.test</code><br>
            <code>127.0.0.1 ifoundadmin.test</code><br>
            <code>127.0.0.1 ifoundsites.test</code><br>
            <code>127.0.0.1 solr.test</code>
        </div>

        <h2>📋 Quick Commands</h2>
        <div class="service">
            <code>docker ps</code> - Check container status<br>
            <code>docker-compose -f docker-compose.simple.yml logs</code> - View logs<br>
            <code>docker-compose -f docker-compose.simple.yml restart</code> - Restart services
        </div>
    </div>
</body>
</html>';
        add_header Content-Type text/html;
    }

    # Proxy to IDX Server API
    location /api/ {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-NginX-Proxy true;

        proxy_pass http://profoundidx-profoundidx-ce839b0c8dc0-newidx-1:8155;
        proxy_redirect off;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Proxy to Solr
    location /solr/ {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-NginX-Proxy true;

        proxy_pass http://profoundidx-profoundidx-ce839b0c8dc0-solr-1:8983;
        proxy_redirect off;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
