Feature: Session handling
  In order to use the site
  As a registered user
  I need to be able to login and logout

  Background: 
    Given a user exists with email: "<EMAIL>", password: "alice123"

  Scenario Outline: Logging in
    Given I am on the login page
    When I fill in user_email with <email>
    And I fill in user_password with <password>
    And I click the button Sign in
    Then I should <action>
    Examples:
      |         email       |  password   |              action             |
      | <EMAIL>         |  alice123   | see "Signed in successfully"    |
      | <EMAIL>     |  password   | see "Invalid email or password" |

