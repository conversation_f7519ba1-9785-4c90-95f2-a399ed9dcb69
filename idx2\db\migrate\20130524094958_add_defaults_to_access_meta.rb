class AddDefaultsToAccessMeta < ActiveRecord::Migration
  def change

    # The commented out values are TEXT fields which may not, in MySQL, have defaults.
    
  	change_column_default :access_meta, :meta_prop_url, ""
  	change_column_default :access_meta, :meta_prop_title, ""
  	change_column_default :access_meta, :meta_prop_h1, ""
  	# change_column_default :access_meta, :meta_prop_keywords, ""
  	# change_column_default :access_meta, :meta_prop_description, ""
  	change_column_default :access_meta, :meta_result_title, ""
  	change_column_default :access_meta, :meta_result_h1, ""
  	# change_column_default :access_meta, :meta_result_keywords, ""
  	# change_column_default :access_meta, :meta_result_description, ""
  	change_column_default :access_meta, :meta_result_prop_h2, ""
  	# change_column_default :access_meta, :meta_result_prop_content, ""
  	change_column_default :access_meta, :meta_cat_title, ""
  	change_column_default :access_meta, :meta_cat_h1, ""
  	# change_column_default :access_meta, :meta_cat_keywords, ""
  	# change_column_default :access_meta, :meta_cat_description, ""
  	change_column_default :access_meta, :meta_geoapi, ""
  	# change_column_default :access_meta, :meta_links_seo, ""
  end
end
