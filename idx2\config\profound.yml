# Reminder: You can override these settings via a .local file.
# So, if this file is named profound.yml, you can override it
# with profound.local.yml.
# The "development" environment here is currently for the sake of allowing
# our email previewing to work, which relies on reading configuration values.
# The "test" environment configured here should give you ideas of what can
# be put in development or production.

defaults: &defaults
  ePN:
    account_number: '080880'
    order_url: https://www.eProcessingNetwork.com/cgi-bin/dbe/order.pl
    logo_url: http://ifoundagent.com/wp-content/blogs.dir/1/files/2013/06/ifound_agent_logo.jpg
  liondesk:
    apikey: XXX

development:
  << : *defaults
  admin_email_addresses:
    - <EMAIL>
    - <EMAIL>
  wpbuilder:
    base_uri: testifoundagent.com

test:
  << : *defaults
  admin_email_addresses:
    - <EMAIL>
    - <EMAIL>
  production_tests:
    email_test:
      from:
        - iFoundAgent Testing <<EMAIL>>
      to:
        - iFoundAgent Testing <<EMAIL>>
  wpbuilder:
    base_uri: testifoundagent.com

production:
  << : *defaults
  wpbuilder:
    base_uri: http://getfoundphx.com
