module GravityForms
  module Crm
    class LionDesk
      def get_event_handler(metadata, data, liondesk)
        if self.is_contact_form data
          Proc.new do
            request_params = self.get_contact_data data
            liondesk.newsubmission request_params
          end
        end
      end

      # To be considered a contact form, it must have first name, and
      # at least email or phone.
      def is_contact_form(data)
        entry = data['entry']
        form = data['form']
        begin
          form_processor = GravityForms::FormProcessor.new entry, form

          return false unless form_processor.first_by_type_then_label('name', 'First')
          return true if form_processor.first_by_type('email').present?
          form_processor.first_by_type('phone').present?
        rescue NoMethodError => ex
          Rails.logger.info('GravityForms::Crm::LionDesk') { "Data is not a contact form" }
          false
        end
      end

      def get_contact_data(data)
        entry = data['entry']
        form = data['form']
        form_processor = GravityForms::FormProcessor.new entry, form

        comments = self.build_comments form, form_processor
        request_params = {
          firstname: form_processor.first_by_type_then_label('name', 'First'),
          lastname: form_processor.first_by_type_then_label('name', 'Last'),
          email: form_processor.first_by_type('email'),
          phone: form_processor.first_by_type('phone'),
          comments: comments
        }

        if data['user_info'] and data['network_id']
          request_params['contactid'] = data['user_info']['username']
          request_params['siteid'] = data['network_id']
        end
        request_params
      end

      # The current use of this is to get a multi-line string that shows the
      # form title and all the values aside from the contact info. To keep it
      # simple at first, I'll just include all fields (as opposed to trying to
      # cut out the contact fields).
      def build_comments(form, form_processor)
        comments = "Form: #{form.andand['title']}\n"
        comments += form_processor.format_fields
      end
    end
  end
end
