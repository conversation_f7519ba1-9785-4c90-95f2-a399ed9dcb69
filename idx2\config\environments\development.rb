Idx2::Application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # In the development environment your application's code is reloaded on
  # every request. This slows down response time but is perfect for development
  # since you don't have to restart the web server when you make code changes.
  config.cache_classes = false

  # Do not eager load code on boot.
  config.eager_load = false

  # Show full error reports and disable caching.
  config.consider_all_requests_local       = true
  config.action_controller.perform_caching = false

  # Don't care if the mailer can't send.
  config.action_mailer.raise_delivery_errors = false

  # Tell Action Mailer not to deliver emails to the real world.
  # The :test delivery method accumulates sent emails in the
  # ActionMailer::Base.deliveries array.
  #config.action_mailer.delivery_method = :letter_opener
  config.action_mailer.delivery_method = :test

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Raise an error on page load if there are pending migrations
  config.active_record.migration_error = :page_load

  # Debug mode disables concatenation and preprocessing of assets.
  config.assets.debug = true

  config.assets.configure do |env|
      env.cache = ActiveSupport::Cache.lookup_store(:null_store)
  end

  config.action_mailer.default_url_options = { host: 'localhost', port: 3000 }
  # config.action_controller.asset_host = "localhost:3000"

  # I added the 'source: :vendorered' based on advice from the following, but it doesn't seem to work.
  # http://feedback.livereload.com/forums/157942-general/suggestions/2988007-fix-livereload-to-work-with-https
  #  (comment from Stefan Wrobel August 19, 2013 6:18 PM)
  config.middleware.insert_after(ActionDispatch::Static, Rack::LiveReload, source: :vendored)

  # This handles cross-origin resource sharing.
  # See: https://github.com/cyu/rack-cors
  config.middleware.insert_before 0, "Rack::Cors" do
    allow do
      # In development, we don't care about the origin.
      origins '*'
      # Reminder: On the following line, the 'methods' refer to the 'Access-
      # Control-Request-Method', not the normal Request Method.
      resource '*', :headers => :any, :methods => [:get, :post, :options, :delete, :put, :patch], credentials: true
    end
  end
end
