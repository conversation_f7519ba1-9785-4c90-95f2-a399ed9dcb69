class AccessMeta < ActiveRecord::Base
  self.table_name = "access_meta"
  belongs_to :client, foreign_key: "access_id"

  before_create :set_default_values
  def set_default_values
    self.meta_prop_keywords = "" if self.meta_prop_keywords == nil
    self.meta_prop_description = "" if self.meta_prop_description == nil
    self.meta_result_keywords = "" if self.meta_result_keywords == nil
    self.meta_result_description = "" if self.meta_result_description == nil
    self.meta_result_prop_content = "" if self.meta_result_prop_content == nil
    self.meta_cat_keywords = "" if self.meta_cat_keywords == nil
    self.meta_cat_description = "" if self.meta_cat_description == nil
    self.meta_links_seo = "" if self.meta_links_seo == nil
  end

  def self.build_from_registration(client)
    mls = AccessMetaDefaults.factory(client.mls)
    meta = client.build_access_meta
    meta.client = client
    mls.set_defaults(meta)
  end

  def self.params_list
    [
      :meta_prop_url,
      :meta_prop_title,
      :meta_prop_h1,
      :meta_prop_keywords,
      :meta_prop_description,
      :meta_result_title,
      :meta_result_h1,
      :meta_result_keywords,
      :meta_result_description,
      :meta_result_prop_h2,
      :meta_result_prop_content,
      :meta_cat_title,
      :meta_cat_h1,
      :meta_cat_keywords,
      :meta_cat_description,
      :meta_geoapi,
      :meta_links_seo,
      :meta_disclosure,
      :meta_address,
      :meta_street_address,
      :meta_pdp_main_label,
      :meta_pdp_main_value,
      :meta_pdp_property_label,
      :meta_pdp_property_value,
      :meta_pdp_general_label,
      :meta_pdp_general_value,
      :meta_pdp_school_label,
      :meta_pdp_school_value,
      :meta_pdp_community_label,
      :meta_pdp_community_value,
      :meta_pdp_lot_label,
      :meta_pdp_lot_value,
      :meta_pdp_rooms_label,
      :meta_pdp_rooms_value,
      :meta_pdp_location_label,
      :meta_pdp_location_value,
      :meta_pdp_features_label,
      :meta_pdp_features_value
    ]
  end
end
