class VideosController < ApplicationController
  before_filter :authenticate_non_admin_user!
  
  def index
  	@title = "Videos"

    authorize! :manage, Video 
    # We hide the fact that split out videos per client from the user.
    @videos = current_user.clients.first.videos
    @video = Video.new
  end

  def new
    redirect_to videos_path
  end

  def create
    @video = Video.new(video_params)
    respond_to do |format|
      if @video.save_for_user(current_user)
        format.html do
          redirect_to videos_path, notice: "Video was successfully added"
        end
      else
        format.html do
          render action: new
        end
      end
    end
  end

  def destroy
    @video = Video.find(params[:id])

    @video.delete_for_user(current_user)

    respond_to do |format|
      format.html { redirect_to videos_path, notice: "Video was successfully deleted" }
    end
  end

private

  def video_params
    params.require(:video).permit(:MLS_id, :youtube_id)
  end
end