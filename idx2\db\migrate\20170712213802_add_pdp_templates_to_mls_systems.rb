class AddPdpTemplatesToMlsSystems < ActiveRecord::Migration
  def change
    add_column :mls_systems, :meta_pdp_property_label, :string, :default => 'Property Description'
    add_column :mls_systems, :meta_pdp_property_value, :text 
    add_column :mls_systems, :meta_pdp_general_label, :string, :default => 'General Information'
    add_column :mls_systems, :meta_pdp_general_value, :text
    add_column :mls_systems, :meta_pdp_school_label, :string, :default => 'School Information'
    add_column :mls_systems, :meta_pdp_school_value, :text
    add_column :mls_systems, :meta_pdp_community_label, :string, :default => 'Community Information'
    add_column :mls_systems, :meta_pdp_community_value, :text
    add_column :mls_systems, :meta_pdp_lot_label, :string, :default => 'Lot Information'
    add_column :mls_systems, :meta_pdp_lot_value, :text
    add_column :mls_systems, :meta_pdp_rooms_label, :string, :default => 'Rooms Information'
    add_column :mls_systems, :meta_pdp_rooms_value, :text
    add_column :mls_systems, :meta_pdp_location_label, :string, :default => 'Property Description'
    add_column :mls_systems, :meta_pdp_location_value, :text

    add_column :access_meta, :meta_pdp_property_label, :string, :default => 'Property Description'
    add_column :access_meta, :meta_pdp_property_value, :text 
    add_column :access_meta, :meta_pdp_general_label, :string, :default => 'General Information'
    add_column :access_meta, :meta_pdp_general_value, :text
    add_column :access_meta, :meta_pdp_school_label, :string, :default => 'School Information'
    add_column :access_meta, :meta_pdp_school_value, :text
    add_column :access_meta, :meta_pdp_community_label, :string, :default => 'Community Information'
    add_column :access_meta, :meta_pdp_community_value, :text
    add_column :access_meta, :meta_pdp_lot_label, :string, :default => 'Lot Information'
    add_column :access_meta, :meta_pdp_lot_value, :text
    add_column :access_meta, :meta_pdp_rooms_label, :string, :default => 'Rooms Information'
    add_column :access_meta, :meta_pdp_rooms_value, :text
    add_column :access_meta, :meta_pdp_location_label, :string, :default => 'Property Description'
    add_column :access_meta, :meta_pdp_location_value, :text
  end
end
