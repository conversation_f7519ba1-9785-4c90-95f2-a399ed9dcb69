.field_mappings
  h2 Field Mappings
  div.well
    strong Add new:
    em< To set additional fields like Display Name, etc, first add, then edit the new field mapping
    p
    form.form-horizontal
      input.form-control> type='text' ng-model="fm.newFieldMapping.mls_class" placeholder="MLS Class"
      input.form-control> type='text' ng-model="fm.newFieldMapping.MapName" placeholder="Map name"
      button.btn ng-click="fm.create()" Create
    strong Show only these fields in table below:
    p
    ul.show-columns
      li ng-repeat="column in fm.cols"
        label.checkbox
          input type="checkbox" ng-model="column.show" {{column.title}}
  div cg-busy="{promise:fm.busyPromise,message:fm.busyMessage,delay:0}"
  toaster-container toaster-options="{'close-button': true}"
  button.btn-success ng-click="fm.reload()"
    i.icon-refresh.icon-white
    |  Reload
  table.table.table-striped.ng-table-responsive ng-table-dynamic="fm.tableParams with fm.cols" show-filter="true"
    tr ng-repeat="map in fm.mappings"
      td ng-if="col.isActions" ng-repeat="col in $columns"
        div.btn-group
          a.btn.btn-default.btn-mini ng-show="!map.$edit" href="javascript:void(0);" ng-click="fm.beginEdit(map)" Edit
          a.btn.btn-default.btn-mini ng-show="map.$edit" href="javascript:void(0);" ng-click="fm.cancelUpdate(map)" Cancel
          a.btn.btn-primary.btn-mini ng-show="map.$edit" href="javascript:void(0);" ng-click="fm.update(map)" Save
          a.btn.btn-danger.btn-mini ng-show="!map.$edit" href="javascript:void(0);" ng-click="fm.deleteMap(map)" X
      td ng-if="!col.isActions" ng-repeat="col in $columns"
        span ng-show="!map.$edit" {{map[col.field]}}
        div ng-show="map.$edit"
          input.form-control ng-model="map[col.field]" ng-keyup="fm.updateOnEnter($event, map)"
