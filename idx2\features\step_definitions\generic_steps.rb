When(/^I fill in (.+) with (.+)$/) do |field, value|
  fill_in field, with: value
end

When(/^I click the button (.+)$/) do |name|
  click_button(name)
end

# This regex matches lines without the word "containing", as that word is
# matched by another step.
When(/^I click on ((?!containing).+)$/) do |thing|
  click_on thing
end

Then /^the number of (.+) should be (.+)$/ do |model, count|
  klass = model.singularize.classify.constantize
  klass.count.should == count.to_i
end

# This regex is meant for clicking something in a particular row in a table.
# You specify what a table row needs to contain for its button (link, etc) to 
# be pushed.
When(/in the row containing (.+), I click on (.+)/) do |name, button|
  # See: http://stackoverflow.com/a/12851999/135101
  page.find(:xpath, "//tr[td[contains(.,'#{name}')]]/td/a", :text => button).click
end

When(/I accept the confirmation dialog/) do
  page.driver.browser.accept_js_confirms
end

Then(/^I should see "(.+)"$/) do |text|
  page.should have_content(text)
end

Then(/^I should not see "(.+)"$/) do |text|
  page.should_not have_content(text)
end

Then /^show me the page$/ do
  save_and_open_page
end

Given(/^I(?: a|')m on (the .+ page)$/) do |page_name|
  visit path_to(page_name)
end

Given(/^I go to (the .+ page)$/) do |page_name|
  visit path_to(page_name)
end

Given(/^the count of table rows should be (\d+)$/) do |count|
  # We add 1 assuming there's a header row.
  page.should have_css("table tr", :count => count.to_i + 1)
end
