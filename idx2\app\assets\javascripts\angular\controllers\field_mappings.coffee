module = angular.module 'pfmlsControllers'

class FieldMappingsCtrl
  constructor: (@fieldMappingSvc, ngTableParams, $filter, @toaster) ->
    @busyMessage = "Loading data"
    params =
      count: 0
      sorting:
        MapName: 'asc'
    settings =
      getData: ($defer, params) =>
        @busyPromise = $defer.promise
        # I can't get a promise array to work. I'm not sure what's wrong. I
        # got it to work in my featuresAdmin controller.
        # @promises.push $defer.promise
        fieldMappingSvc.getAllMappings().getList().then (fieldMappings) =>
          # console.log fieldMappings.plain()[0]
          filteredData = if params.filter() then $filter('filter')(fieldMappings, params.filter()) else fieldMappings
          orderedData = if params.sorting() then $filter('orderBy')(filteredData, params.orderBy()) else filteredData
          @mappings = orderedData
          params.total orderedData.length
          $defer.resolve(orderedData)
    @tableParams = new ngTableParams params, settings
    @cols = @computeCols()
    @newFieldMapping = @makeNewFieldMapping()
  # promises: []
  MLSs: [
    'armls'
    'glvarnv'
    'mredil'
    'paaraz'
    'paaraz_mlg'
    'tarmlsaz'
    'sdcrca'
    'trendmls'
    'brightmls'
    'naar'
    'cabor'
    'wmar'
    'crmls'
    'recolorado'
    'recolorado_mlg'
    'wardex'
    'realtracs'
    'armls_spark'
    'naar_spark'
    # This is temporary. We should delete this line and this column after we're done with the initial sync.
    'naar_spark_init_20250710'
  ]
  count: 0
  showColumn: (mls) ->
    mls in @MLSs
  computeCols: ->
    cols = [
      {
        title: 'Actions'
        isActions: true
        show: true
      }
      {
        title: 'MLS Class'
        filter: { mls_class: 'text' }
        show: true
        field: 'mls_class'
      }
      {
        title: 'Map Name'
        filter: { MapName: 'text' }
        show: true
        field: 'MapName'
      }
      {
        title: 'Display Name'
        filter: { DisplayName: 'text' }
        show: true
        field: 'DisplayName'
      }
      {
        title: 'Easy Name'
        filter: { EasyName: 'text' }
        show: true
        field: 'EasyName'
      }
      {
        title: 'Type'
        filter: { Type: 'text' }
        show: true
        field: 'Type'
      }
    ]
    for mls in @MLSs
      x = {
        title: mls
        filter: {}
        show: false
        field: mls
      }
      x.filter[mls] = 'text'
      cols.push x
    cols
  makeNewFieldMapping: -> { mls_class: null, MapName: null }
  create: =>
    p = @fieldMappingSvc.create @newFieldMapping
    p.then =>
      @toaster.success "Success", "Added #{@newFieldMapping.mls_class}, #{@newFieldMapping.MapName}"
      @newFieldMapping = @makeNewFieldMapping()
      @reload()
    , (response) =>
      @toaster.error "Failed to create field mapping", response.data
    @busyPromise = p
  reload: =>
    @tableParams.reload()
  oldValues: {}
  beginEdit: (map) =>
    @oldValues[map.id] = map.plain()
    map.$edit = true
  cancelUpdate: (map) =>
    oldMap = @oldValues[map.id]
    for field of oldMap
      map[field] = oldMap[field]
    map.$edit = false
  updateOnEnter: (event, map) =>
    if event.which == 13
      @update map
  update: (map) =>
    p = map.save()
    p.then =>
      @toaster.success "Success", "Updated!"
      map.$edit = false
    , (response) =>
      @toaster.error "Failed to update", response.data
    @busyPromise = p
  deleteMap: (map) =>
    return unless confirm("Are you sure you wish to delete (#{map.mls_class}, #{map.MapName})?")
    p = map.remove()
    p.then =>
      @toaster.success "Success", "Deleted #{map.mls_class}, #{map.MapName}"
      index = @mappings.indexOf map
      @mappings.splice index, 1
    , (response) =>
      @toaster.error "Failed to delete", response.data
    @busyPromise = p

FieldMappingsCtrl.$inject = ['fieldMappingSvc', 'NgTableParams', '$filter', 'toaster']
module.controller 'FieldMappingsCtrl', FieldMappingsCtrl
