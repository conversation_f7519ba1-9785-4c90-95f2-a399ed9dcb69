
# This file can contain anything extra.

# For the moment, it's a reminder of what I had to do to get the glyphs working for twitter bootstrap. The image filse weren't copied from the gem when I ran 'rake assets:precompile' (adding 'RAILS_ENV=production') didn't help. So I found the images in the gem and moved them to production by hand.

namespace :extras do
  desc "Symlink the profound.local.yml file into latest release"
  task :symlink, roles: :app do
    # Only do this if the profound.local.yml file exists.
    run "[ -e #{shared_path}/config/profound.local.yml ] && ln -nfs #{shared_path}/config/profound.local.yml #{release_path}/config/profound.local.yml"
  end
  after "deploy:finalize_update", "extras:symlink"
end