class ClientDefaults < ActiveRecord::Migration
  def change
  	change_column_default :access, :access_member_id, ""
  	change_column_default :access, :access_office_id, ""
  	change_column_default :access, :domain, ""
  	change_column_default :access, :ftp_addr, ""
  	change_column_default :access, :ftp_user, ""
  	change_column_default :access, :ftp_pass, ""
  	change_column_default :access, :access_domain, ""
  	change_column_default :access, :access_ip, ""
  	change_column_default :access, :access_company, ""
  	change_column_default :access, :access_fullname, ""
  	change_column_default :access, :access_address, ""
  	change_column_default :access, :access_address2, ""
  	change_column_default :access, :access_city, ""
  	change_column_default :access, :access_state, ""
  	change_column_default :access, :access_zipcode, ""
  	change_column_default :access, :access_phone, ""
  	change_column_default :access, :access_emailaddress, ""
  end
end
