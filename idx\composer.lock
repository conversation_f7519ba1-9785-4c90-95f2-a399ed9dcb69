{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "content-hash": "5e50dbc4683163cfe2564aa68fff95c8", "packages": [{"name": "aws/aws-sdk-php", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "b04efb3c73785a3c0b6e6c34740adaaad4d9fc94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/b04efb3c73785a3c0b6e6c34740adaaad4d9fc94", "reference": "b04efb3c73785a3c0b6e6c34740adaaad4d9fc94", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~5.3|~6.0.1|~6.1", "guzzlehttp/promises": "~1.0", "guzzlehttp/psr7": "~1.0", "mtdowling/jmespath.php": "~2.2", "php": ">=5.5"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-json": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-simplexml": "*", "ext-spl": "*", "nette/neon": "^2.3", "phpunit/phpunit": "~4.0"}, "suggest": {"doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Aws\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "time": "2015-11-03T19:40:35+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d094e337976dff9d8e2424e8485872194e768662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d094e337976dff9d8e2424e8485872194e768662", "reference": "d094e337976dff9d8e2424e8485872194e768662", "shasum": ""}, "require": {"guzzlehttp/promises": "~1.0", "guzzlehttp/psr7": "~1.1", "php": ">=5.5.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0", "psr/log": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2016-03-21T20:02:09+00:00"}, {"name": "guzzlehttp/promises", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "b1e1c0d55f8083c71eda2c28c12a228d708294ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/b1e1c0d55f8083c71eda2c28c12a228d708294ea", "reference": "b1e1c0d55f8083c71eda2c28c12a228d708294ea", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2015-10-15T22:28:00+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "31382fef2889136415751badebbd1cb022a4ed72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/31382fef2889136415751badebbd1cb022a4ed72", "reference": "31382fef2889136415751badebbd1cb022a4ed72", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "PSR-7 message implementation", "keywords": ["http", "message", "stream", "uri"], "time": "2016-04-13T19:56:01+00:00"}, {"name": "hassankhan/config", "version": "0.10.0", "source": {"type": "git", "url": "https://github.com/hassankhan/config.git", "reference": "06ac500348af033f1a2e44dc357ca86282626d4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hassankhan/config/zipball/06ac500348af033f1a2e44dc357ca86282626d4a", "reference": "06ac500348af033f1a2e44dc357ca86282626d4a", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.0", "scrutinizer/ocular": "~1.1", "squizlabs/php_codesniffer": "~2.2"}, "suggest": {"symfony/yaml": "~2.5"}, "type": "library", "autoload": {"psr-4": {"Noodlehaus\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://hassankhan.me/", "role": "Developer"}], "description": "Lightweight configuration file loader that supports PHP, INI, XML, JSON, and YAML files", "homepage": "http://hassankhan.me/config/", "keywords": ["config", "configuration", "ini", "json", "microphp", "unframework", "xml", "yaml", "yml"], "time": "2016-02-11T16:21:17+00:00"}, {"name": "monolog/monolog", "version": "1.17.2", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "bee7f0dc9c3e0b69a6039697533dca1e845c8c24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/bee7f0dc9c3e0b69a6039697533dca1e845c8c24", "reference": "bee7f0dc9c3e0b69a6039697533dca1e845c8c24", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "raven/raven": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "swiftmailer/swiftmailer": "~5.3", "videlalvaro/php-amqplib": "~2.4"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "php-console/php-console": "Allow sending log messages to Google Chrome", "raven/raven": "Allow sending log messages to a Sentry server", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "videlalvaro/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.16.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2015-10-14T12:51:02+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "a7d99d0c836e69d27b7bfca1d33ca2759fba3289"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/a7d99d0c836e69d27b7bfca1d33ca2759fba3289", "reference": "a7d99d0c836e69d27b7bfca1d33ca2759fba3289", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2015-05-27T17:21:31+00:00"}, {"name": "psr/http-message", "version": "1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "85d63699f0dbedb190bbd4b0d2b9dc707ea4c298"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/85d63699f0dbedb190bbd4b0d2b9dc707ea4c298", "reference": "85d63699f0dbedb190bbd4b0d2b9dc707ea4c298", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2015-05-04T20:22:00+00:00"}, {"name": "psr/log", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe0936ee26643249e916849d48e3a51d5f5e278b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe0936ee26643249e916849d48e3a51d5f5e278b", "reference": "fe0936ee26643249e916849d48e3a51d5f5e278b", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Psr\\Log\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "keywords": ["log", "psr", "psr-3"], "time": "2012-12-21T11:40:51+00:00"}, {"name": "suin/php-rss-writer", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/suin/php-rss-writer.git", "reference": "96ff59575442dbe23e15fe7ff56d0bf27486cff9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/suin/php-rss-writer/zipball/96ff59575442dbe23e15fe7ff56d0bf27486cff9", "reference": "96ff59575442dbe23e15fe7ff56d0bf27486cff9", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"Suin\\RSSWriter": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON> aka <PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Yet another simple RSS writer library for PHP 5.3 or later.", "homepage": "https://github.com/suin/php-rss-writer", "keywords": ["feed", "generator", "php", "rss", "writer"], "time": "2015-08-21T06:24:39+00:00"}], "packages-dev": [{"name": "chriskite/phactory", "version": "v0.4.3", "source": {"type": "git", "url": "https://github.com/chriskite/phactory.git", "reference": "fa0140e27992dbcc5d70c523f738c373d5a75061"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/chriskite/phactory/zipball/fa0140e27992dbcc5d70c523f738c373d5a75061", "reference": "fa0140e27992dbcc5d70c523f738c373d5a75061", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"ext-mongo": "*", "ext-pdo": "*", "ext-pdo_sqlite": "*"}, "suggest": {"ext-mongo": "Allows use of Phactory for testing against MongoDB databases", "ext-pdo": "Allows use of Phactory for testing against SQL databases", "ext-pdo_mysql": "Allows use of Phactory for testing against MySQL databases", "ext-pdo_sqlite": "Allows use of Phactory for testing against SQLite databases"}, "type": "library", "autoload": {"psr-0": {"Phactory": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "description": "A Database Factory for PHP Unit Tests", "homepage": "http://phactory.org", "keywords": ["database", "mongodb", "mysql", "sqlite", "testing"], "time": "2013-07-11T01:48:16+00:00"}, {"name": "guzzle/guzzle", "version": "v3.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle3.git", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle3/zipball/0645b70d953bc1c067bbc8d5bc53194706b628d9", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3.3", "symfony/event-dispatcher": "~2.1"}, "replace": {"guzzle/batch": "self.version", "guzzle/cache": "self.version", "guzzle/common": "self.version", "guzzle/http": "self.version", "guzzle/inflection": "self.version", "guzzle/iterator": "self.version", "guzzle/log": "self.version", "guzzle/parser": "self.version", "guzzle/plugin": "self.version", "guzzle/plugin-async": "self.version", "guzzle/plugin-backoff": "self.version", "guzzle/plugin-cache": "self.version", "guzzle/plugin-cookie": "self.version", "guzzle/plugin-curlauth": "self.version", "guzzle/plugin-error-response": "self.version", "guzzle/plugin-history": "self.version", "guzzle/plugin-log": "self.version", "guzzle/plugin-md5": "self.version", "guzzle/plugin-mock": "self.version", "guzzle/plugin-oauth": "self.version", "guzzle/service": "self.version", "guzzle/stream": "self.version"}, "require-dev": {"doctrine/cache": "~1.3", "monolog/monolog": "~1.0", "phpunit/phpunit": "3.7.*", "psr/log": "~1.0", "symfony/class-loader": "~2.1", "zendframework/zend-cache": "2.*,<2.3", "zendframework/zend-log": "2.*,<2.3"}, "suggest": {"guzzlehttp/guzzle": "Guzzle 5 has moved to a new package name. The package you have installed, Guzzle 3, is deprecated."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.9-dev"}}, "autoload": {"psr-0": {"Guzzle": "src/", "Guzzle\\Tests": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "Guzzle Community", "homepage": "https://github.com/guzzle/guzzle/contributors"}], "description": "PHP HTTP client. This library is deprecated in favor of https://packagist.org/packages/guzzlehttp/guzzle", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "abandoned": "guzzlehttp/guzzle", "time": "2015-03-18T18:23:50+00:00"}, {"name": "phpunit/php-code-coverage", "version": "1.2.18", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "fe2466802556d3fe4e4d1d58ffd3ccfd0a19be0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/fe2466802556d3fe4e4d1d58ffd3ccfd0a19be0b", "reference": "fe2466802556d3fe4e4d1d58ffd3ccfd0a19be0b", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": ">=1.3.0@stable", "phpunit/php-text-template": ">=1.2.0@stable", "phpunit/php-token-stream": ">=1.1.3,<1.3.0"}, "require-dev": {"phpunit/phpunit": "3.7.*@dev"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["PHP/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2014-09-02T10:13:14+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/6150bf2c35d3fc379e50c7602b75caceaa39dbf0", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2015-06-21T13:08:43+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3e82f4e9fc92665fafd9157568e4dcb01d014e5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3e82f4e9fc92665fafd9157568e4dcb01d014e5b", "reference": "3e82f4e9fc92665fafd9157568e4dcb01d014e5b", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2015-06-21T08:01:12+00:00"}, {"name": "phpunit/php-token-stream", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "ad4e1e23ae01b483c16f600ff1bebec184588e32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/ad4e1e23ae01b483c16f600ff1bebec184588e32", "reference": "ad4e1e23ae01b483c16f600ff1bebec184588e32", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["PHP/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2014-03-03T05:10:30+00:00"}, {"name": "phpunit/phpunit", "version": "3.7.19", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "3.7.19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/3.7.19", "reference": "3.7.19", "shasum": ""}, "require": {"ext-dom": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=5.3.3", "phpunit/php-code-coverage": ">=1.2.1,<1.3.0", "phpunit/php-file-iterator": ">=1.3.1", "phpunit/php-text-template": ">=1.1.1", "phpunit/php-timer": ">=1.0.2,<1.1.0", "phpunit/phpunit-mock-objects": ">=1.2.0,<1.3.0", "symfony/yaml": ">=2.0.0,<2.3.0"}, "require-dev": {"pear-pear/pear": "1.9.4"}, "suggest": {"ext-json": "*", "ext-simplexml": "*", "ext-tokenizer": "*", "phpunit/php-invoker": ">=1.1.0,<1.2.0"}, "bin": ["composer/bin/phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.7.x-dev"}}, "autoload": {"classmap": ["PHPUnit/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["", "../../symfony/yaml/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "http://www.phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2013-03-25T11:45:06+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "1.2.3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/1.2.3", "reference": "1.2.3", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-text-template": ">=1.1.1@stable"}, "suggest": {"ext-soap": "*"}, "type": "library", "autoload": {"classmap": ["PHPUnit/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2013-01-13T10:24:48+00:00"}, {"name": "symfony/event-dispatcher", "version": "v2.7.6", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "87a5db5ea887763fa3a31a5471b512ff1596d9b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/87a5db5ea887763fa3a31a5471b512ff1596d9b8", "reference": "87a5db5ea887763fa3a31a5471b512ff1596d9b8", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.0,>=2.0.5", "symfony/dependency-injection": "~2.6", "symfony/expression-language": "~2.6", "symfony/stopwatch": "~2.3"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2015-10-11T09:39:48+00:00"}, {"name": "symfony/yaml", "version": "v2.2.11", "target-dir": "Symfony/Component/Yaml", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "4f3569bb23953116281fb3a642c4266ce84a2f44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/4f3569bb23953116281fb3a642c4266ce84a2f44", "reference": "4f3569bb23953116281fb3a642c4266ce84a2f44", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-0": {"Symfony\\Component\\Yaml\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "http://symfony.com", "time": "2013-11-25T10:21:43+00:00"}, {"name": "zendframework/zendframework1", "version": "1.12.4", "source": {"type": "git", "url": "https://github.com/zendframework/zf1.git", "reference": "06a43c8c16aed263092ad1c352f25856233edaa1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zf1/zipball/06a43c8c16aed263092ad1c352f25856233edaa1", "reference": "06a43c8c16aed263092ad1c352f25856233edaa1", "shasum": ""}, "require": {"php": ">=5.2.11"}, "require-dev": {"phpunit/dbunit": "1.3.*", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.12.x-dev"}}, "autoload": {"psr-0": {"Zend_": "library/"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["library/"], "license": ["BSD-3-<PERSON><PERSON>"], "description": "Zend Framework 1", "homepage": "http://framework.zend.com/", "keywords": ["ZF1", "framework"], "time": "2014-03-06T17:53:46+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform-dev": []}