class ApplicationController < ActionController::Base
  # Prevent CSRF attacks by raising an exception.
  #
  # UPDATE: I think I now have this figured out such that I don't need this previous workaround.
  # Glancing at it, it doesn't look secure anyway: don't do XSRF forgery protection if it's JSON?
  # That seems foolish, especially considering it's the most likely type of XSRF isn't it?
  # So take this cruft out on next commit.
  #
  # But don't use CSRF protection for API requests.
  # Slightly modified from: http://stackoverflow.com/a/10049965/135101.
  # As a reminder, it seems that the authenticity_token value in the POST data is what matters, and not
  # the XSRF-TOKEN cookie passed as a header that matters. I'm not sure why both are included; perhaps
  # the XSRF-TOKEN is legacy? In my tests, they had the same value.
  # protect_from_forgery with: :exception, unless: Proc.new { |c| c.request.format == 'application/json' }
  helper_method :destroy_admin_user_session_path
  helper_method :current_reseller
  helper_method :current_theme

  # I got this from: https://gist.github.com/schneikai/9171887
  include SSLWithConfiguredPort

  def authenticate_custom_admin_user!
    render :not_authorized, status: 403 and return if user_signed_in? && !current_user.admin?
    authenticate_user!
  end

  def authenticate_non_admin_user!
  	if user_signed_in? && current_user.admin?
      flash[:error] = "That section of the website is for non-administrators. You're an admin, so you have your own section."
      redirect_to "/custom_admin#{request.fullpath}" and return
  	end
  	authenticate_user!
  end

  def authenticate_client_user!
    if !(user_signed_in? && !current_user.admin? && current_user.clients.count > 0)
      flash[:error] = "This page is meant for client users"
      redirect_to must_be_client_path and return
    end
  end

  def authenticate_require_not_signed_in!
    if user_signed_in?
      flash[:error] = "That page is meant for users that aren't signed in. If you want to see it, sign out first."
      redirect_to must_be_signed_out_path and return
    end
  end

  def authenticate_admin_user!
    # My original idea with custom admin users was to keep it separate from
    # admin users like in active_admin. Now that seems unnecessary. So for
    # active admin, we'll just piggy back.
    authenticate_custom_admin_user!
  end

  def prepare_whitelabel
    if params['reseller_friendlyid'].present?
      reseller_friendlyid = params['reseller_friendlyid']
      begin
        reseller = Reseller.friendly.find reseller_friendlyid
        if reseller
          @reseller = reseller
          prepend_view_path ["app/views/whitelabel/#{reseller.friendlyid}", "app/views/whitelabel"]
        end
      rescue
        flash.now[:warning] = "'#{params['reseller_friendlyid']}' is an invalid reseller friendly id"
      end
    end
  end

  # I originally was storing the current reseller in the session. I don't like
  # that idea. It seems ripe for bugs / mis-intentions, and importantly, it's
  # a pain to deal with in development.
  def current_reseller
    @reseller
  end

  def current_theme
    @theme || @registration.try(:theme)
  end

  def allow_iframe_requests
    response.headers.delete('X-Frame-Options')
  end

  def self.responder
    ResponderWithPutContent
  end
end
