
# This file is meant for generic database stuff like seeding data,
# as opposed to a particular platform like mysql/postgres which would go in its own file.

namespace :db do
  desc "Symlink the database.yml file into latest release"
  task :symlink, roles: :app do
    run "ln -nfs #{shared_path}/config/database.yml #{release_path}/config/database.yml"
  end
  after "deploy:finalize_update", "db:symlink"

  desc "Seed the database"
  task :seed do
  	run "mkdir -p #{shared_path}/db/seeds"
  	# Sync our files that aren't in the git repo like password files.
    find_servers_for_task(current_task).each do |server|
      run_locally "rsync -vr db/seeds/passwords.yml #{user}@#{server.host}:#{shared_path}/db/seeds/passwords.yml"
    end
    # Delete the symlink if needed.
    run "test -h #{current_path}/db/seeds/passwords.yml && rm #{current_path}/db/seeds/passwords.yml"
    run "ln -s #{shared_path}/db/seeds/passwords.yml #{current_path}/db/seeds/passwords.yml"
    run "cd #{current_path}; bundle exec rake db:seed RAILS_ENV=#{rails_env}"
    run "rm #{shared_path}/db/seeds/passwords.yml"
  end
  # It doesn't feel right to have this run every time. It seems like it 
  # could surprise someone to (e.g.) delete some record but then see it
  # reincarnated after the next deploy. I think we should make this
  # a more explicit step when it's necessary.
  # after "deploy:finalize_update", "db:seed"
end
