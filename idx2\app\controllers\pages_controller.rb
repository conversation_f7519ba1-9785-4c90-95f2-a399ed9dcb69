require 'yaml'

class PagesController < ApplicationController
  force_ssl_with_configured_port only: [:payment_approved, :pay]
  before_filter :allow_iframe_requests, only: [:registration_success, :registration_success_idx_access_only, :pay]
  before_filter :prepare_whitelabel, only: [:registration_success, :registration_success_idx_access_only, :pay]

  def main
    @title = "Welcome"
  end

  def must_be_signed_out
    @title = "You must be signed out"
    render text: '', layout: true
  end

  def registration_success
    @theme = params[:theme]

    @title = "Registration successful!"
    promo_code = params[:promo_code]
    @payment_url = 'blah'
  end

  def registration_success_idx_access_only
    @title = "Registration successful!"
  end

  def must_be_client
    @title = "Client page"
    render text: '', layout: true
  end

  def payment_approved
    if request.post?
      if user_signed_in?
        registration = current_user.get_registration
      else
        registration = Registration.find(params[:ID])
      end
      epn_response_yaml = YAML.dump params.to_h
      # I was getting the error Mysql2::Error: Incorrect string value.
      # So for simplicity I just remove the UTF-8 values.
      # I got the following from: http://stackoverflow.com/a/19793570/135101
      epn_response_yaml_proper_utf8 = epn_response_yaml.encode('utf-8', 'binary', invalid: :replace, undef: :replace, replace: '')
      registration.epn_response = epn_response_yaml_proper_utf8
      registration.save!
      registration.mark_paid
    end
  end

  def pay
    @title = "Pay for IDX subscription"
    @reseller_pricing = nil
    if params[:promo_code]
      @reseller_pricing = ResellerPricing.friendly.find params[:promo_code]
    end
  end
end
