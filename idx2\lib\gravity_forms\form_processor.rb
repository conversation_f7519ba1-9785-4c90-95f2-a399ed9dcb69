module GravityForms
  class FormProcessor
    attr_accessor :entry, :form
    protected :entry, :form

    def initialize(entry, form)
      self.entry = entry
      self.form = form
    end

    def first_by_label(label)
      self.entry_value self.first_by('label', label)
    end

    def first_by_type(type)
      self.entry_value self.first_by('type', type)
    end

    def first_by_type_then_label(type, label)
      field = first_by 'type', type
      self.entry_value(first_by 'label', label, form: field, container: 'inputs')
    end

    def first_by(by, value, form: self.form, container: 'fields')
      form[container].values.find { |h| h[by] == value }
    end

    def entry_value(field)
      return nil unless field
      self.entry[field['id']]
    end

    def format_fields(form: self.form, container: 'fields')
      message_lines = []
      form[container].values.each do |field|
        if field.key?('inputs') && !field['inputs'].blank?
          message_lines.push(self.format_fields(form: field, container: 'inputs'))
        else
          message_lines.push(self.format_field field)
        end
      end
      message_lines.reject(&:nil?).join "\n"
    end

    def format_field(field)
      return nil unless self.entry_value field
      "#{field['label']}: #{self.entry_value field}"
    end
  end
end
