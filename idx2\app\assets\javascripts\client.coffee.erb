class Registration
  init: ->
    if /tierra-antigua-website-idx-signup/.test document.referrer
      for theme in ["Rysky", "Rysky Too", "Rysky Pro", "Sky", "Ivy", "8", "84"]
        Array.prototype.push.apply @themeToColorSchemesMap[theme], ["Purple", "Turquoise", "Brown"]
      Array.prototype.push.apply @themeToColorSchemesMap["Eleven43"], ["Purple", "Turquoise", "Brown", "Tierra"]
      for theme in ["Prime", "Summit", "Summit V"]
        Array.prototype.push.apply @themeToColorSchemesMap[theme], ["Tierra"]
  themeToColorSchemesMap: <%= Registration.get_theme_to_color_schemes_map.to_json %>
  getMappedColorScheme: (theme) ->
    @themeToColorSchemesMap[theme].sort()

registration = new Registration()
registration.init()

$ ->
  $(".pfmls_idx_update").on "click", "a.run_update", (ev) ->
    ev.preventDefault()
    target = $(ev.target)
    url = target.data("url")
    $(".pfmls_idx_update .load_message").html("Attempting to load URL: #{url}")
    $("#pfmls_idx_update_iframe").attr('src', url)
    $("#pfmls_idx_update_iframe").attr('height', '800px')

  $colorSchemeHidden = $("input[name='client[registration][color_scheme]'][type='hidden']")

  changeWusaMls = (theme) ->
    new_mls = switch theme
      when "White Mountains (WMAR)"
        "wmar"
      when "Payson (CABOR)"
        "cabor"
      else
        "armls"

    if new_mls isnt "default"
      $("#client_mls").val new_mls

  updateThemesPerCategory = (cat_id) ->
    themes = $("#client_registration_theme")
    if cat_id is "cat_site_free"
      themes.children("option").prop("disabled", false)
      themes.children("option[value='White Mountains (WMAR)']").prop("disabled", false)
      themes.children("option[value='Payson (CABOR)']").prop("disabled", false)
      $("#client_feature_gui").val "0"
      themes.change()
    else if cat_id is "cat_site_marketing"
      themes.val("Marketing Platform Only")
      themes.children("option").prop("disabled", false)
      themes.children("option:not(:selected)").prop("disabled", true)
      themes.change()
    else
      themes.val("Jinger")
      themes.children("option").prop("disabled", false)
      themes.children("option[value='Phoenix (ARMLS)']").prop("disabled", true)
      themes.children("option[value='White Mountains (WMAR)']").prop("disabled", true)
      themes.children("option[value='Payson (CABOR)']").prop("disabled", true)
      themes.children("option[value='Marketing Platform Only']").prop("disabled", true)
      $("#client_feature_gui").val "1"
      themes.change()

  changeColorChoicesByTheme = (theme) ->
    colorSchemes = registration.getMappedColorScheme theme
    newOptions = {}
    $.each colorSchemes, (index, val) ->
      newOptions[val] = val

    $el = $("#client_registration_color_scheme");
    if $('#client_registration_color_scheme option').first().val() == "<Please select your color scheme>"
      # Remove all options except the first
      # From: http://stackoverflow.com/a/1801515/135101
      $('#client_registration_color_scheme option:gt(0)').remove()
    else
      $('#client_registration_color_scheme option').remove()
      $el.append $("<option></option").attr("value", '').text("<Please select your color scheme>")
    $.each newOptions, (key, value) ->
      $el.append $("<option></option>").attr("value", value).text(key);

  # Load initial value, which we need to do if there was an error posting the form.
  $theme = $("select[name='client[registration][theme]']")
  theme = $theme.val()
  $colorScheme = $("select[name='client[registration][color_scheme]']")
  if theme
    changeColorChoicesByTheme theme
    $colorScheme.val $colorSchemeHidden.val()
    changeWusaMls theme

  cat_id = $('input[type="radio"][name="helper[cat_site]"]:checked').val()
  updateThemesPerCategory cat_id

  $("#client_registration_theme").on "change", ->
    $target = $(this)
    theme = $target.val()
    changeColorChoicesByTheme theme
    changeWusaMls theme
    $colorSchemeHidden.val ''

  $colorScheme.on 'change', ->
    $colorSchemeHidden.val $(this).val()

  $("#client_registration_theme").children("option[value='Marketing Platform Only']").prop("disabled", true)

  $('input[type="radio"][name="helper[cat_site]"]').on 'click', (e) ->
    updateThemesPerCategory this.id

  # This is for reseller sites that iframe in this form. We send them an event
  # letting them know the button was pushed. They can do what they wish on
  # that event, but the original idea is to allow them to scroll to the top of
  # the page to see errors, or to see the registration success message.
  $(".pfmls-get-started-form-submit-button").on 'click', (evt) ->
    parent.postMessage("pfmls-get-started-form-submit","*")
