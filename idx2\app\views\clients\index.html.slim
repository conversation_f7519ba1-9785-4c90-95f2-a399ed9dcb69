.client_index_page
  h2 Clients
  .row
    .span12
      table.table.table-striped.table-bordered.auto-width.clients-list
        tr
          th = sort_link(@q, :id)
          th = sort_link(@q, :access_company)
          th = sort_link(@q, :access_fullname, [:access_fullname, 'access_company asc'])
          th = sort_link(@q, :access_apikey)
          th Action
        - for client in @clients
          tr
            td = link_to client.id, client_path(client)
            td = link_to (client.company.presence ? client.company : "(no name)"), client_path(client)
            td = link_to (client.name.presence ? client.name : "(no name)"), client_path(client)
            td = client.access_apikey
            td = link_to "<i class='icon-remove icon-white'></i> delete".html_safe, client_path(client), method: :delete, class: "btn btn-danger btn-small", data: { confirm: "Are you sure you want to delete client #{client.access_company}?" }
