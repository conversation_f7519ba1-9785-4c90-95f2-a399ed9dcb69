.mls_systems_edit_page
  h1= "Edit #{@mls_system.display_name} Settings"

  = simple_form_for @mls_system, html: { class: "form-horizontal" }, defaults: { input_html: { class: "edit_field" } } do |f|
    h2 SEO defaults
    .seo_defaults
      h3 Category
      .category
        = f.input :meta_cat_title
        = f.input :meta_cat_h1
        = f.input :meta_cat_description, input_html: { class: "edit_field tall" }
        = f.input :meta_cat_keywords, input_html: { class: "edit_field tall" }
      h3 Search Results
      .search_results
        = f.input :meta_result_title
        = f.input :meta_result_h1
        = f.input :meta_result_description
        = f.input :meta_result_prop_h2
        = f.input :meta_result_prop_content, input_html: { class: "edit_field tall" }
      h3 Property
      .property
        = f.input :meta_prop_url
        = f.input :meta_prop_title
        = f.input :meta_prop_h1
        = f.input :meta_prop_description, input_html: { class: "edit_field tall" }
        = f.input :meta_prop_keywords, input_html: { class: "edit_field tall" }

    h2 Templates
    .templates
      = f.input :meta_disclosure, hint: 'Reminder that this discloure value is ignored, as it\'s now controlled by ifoundadmin.com, see e.g. https://mls-associations.ifoundadmin.com/wp-admin/post.php?post=242&action=edit'
      = f.input :meta_address
      = f.input :meta_street_address
      h3 Property Display Page
      .property-display-page
        h4 PDP Main Content
        .property_description
          = f.input :meta_pdp_main_label
          = f.input :meta_pdp_main_value, input_html: { class: "edit_field tall" }
        h4 Property Description
        .property_description
          = f.input :meta_pdp_property_label
          = f.input :meta_pdp_property_value, input_html: { class: "edit_field tall" }
        h4 General Information
        .general_information
          = f.input :meta_pdp_general_label
          = f.input :meta_pdp_general_value, input_html: { class: "edit_field tall" }
        h4 Property Features
        .location_information
          = f.input :meta_pdp_features_label
          = f.input :meta_pdp_features_value, input_html: { class: "edit_field tall" }
        h4 School Information
        .school_information
          = f.input :meta_pdp_school_label
          = f.input :meta_pdp_school_value, input_html: { class: "edit_field tall" }
        h4 Community Information
        .community_information
          = f.input :meta_pdp_community_label
          = f.input :meta_pdp_community_value, input_html: { class: "edit_field tall" }
        h4 Lot Information
        .lot_information
          = f.input :meta_pdp_lot_label
          = f.input :meta_pdp_lot_value, input_html: { class: "edit_field tall" }
        h4 Rooms Information
        .rooms_information
          = f.input :meta_pdp_rooms_label
          = f.input :meta_pdp_rooms_value, input_html: { class: "edit_field tall" }
        h4 Location Information
        .location_information
          = f.input :meta_pdp_location_label
          = f.input :meta_pdp_location_value, input_html: { class: "edit_field tall" }
      = f.submit "Save default values", class: "btn-primary"
      | &nbsp;
      = link_to "Cancel edit", mls_system_path(@mls_system), class: "btn"
