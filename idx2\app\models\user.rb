class User < ActiveRecord::Base
  has_many :clients, through: :user_client
  has_many :user_client
  has_many :videos, through: :clients
  
  # Include default devise modules. Others available are:
  # :token_authenticatable, :confirmable,
  # :lockable, :timeoutable and :omniauthable
  devise :database_authenticatable,
         :recoverable, :rememberable, :trackable, :validatable

  def self.build_from_registration(client)
    # Remember this automatically adds the user to the client record.
    client.users.build(email: client.access_emailaddress, password: build_random_password)
  end

  def self.build_random_password
    Devise.friendly_token.first(8)
  end

  def has_paid?
    # If this user has multiple clients, we can't determine if they've paid.
    !self.admin && self.clients.count == 1 && self.get_registration.payment_state == "paid"
  end

  # This method is meant as a safety placeholder. Right now I'm calling it from
  # places where I know it's not the right solution, but for the moment I
  # haven't really thought through how to handle multiple registrations attached
  # to one user. If that happens, it'll raise an error and let us know that it's
  # time to look into it.
  def get_registration
    if self.clients.count == 1
      self.clients.first.registration
    else
      raise "Cannot get registration when user has multiple clients"
    end
  end
end
