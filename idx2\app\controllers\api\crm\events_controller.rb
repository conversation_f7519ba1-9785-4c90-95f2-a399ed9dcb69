module Api
  module Crm
    class EventsController < ApplicationController
      respond_to :json

      def go
        apikey = params[:apikey]
        verified, client = verify_apikey(apikey)
        return unless client
        metadata = params[:metadata]
        render json: { error_message: "Metadata invalid or not supplied" } and return unless metadata.kind_of?(Hash)
        event_name = metadata['event_name']
        render json: { error_message: "Invalid metadata event_name: #{event_name}" } and return unless event_name
        CrmApi.dispatch_for_all_crms(client, metadata, params['data'])
        render json: { message: "ok" }
      end

      def verify_apikey(apikey)
        clients = Client.where(apikey: apikey)
        render json: { error_message: "Invalid API key: #{apikey}" } and return false if clients.size == 0
        render json: { error_message: "API key matches multiple clients: #{apikey}" } and return false if clients.size > 1
        return true, clients.first
      end
    end
  end
end
