// This is a manifest file that'll be compiled into application.js, which will include all the files
// listed below.
//
// Any JavaScript/Coffee file within this directory, lib/assets/javascripts, vendor/assets/javascripts,
// or vendor/assets/javascripts of plugins, if any, can be referenced here using a relative path.
//
// It's not advisable to add code directly here, but if you do, it'll appear at the bottom of the
// compiled file.
//
// WARNING: THE FIRST BLANK LINE MARKS THE END OF WHAT'S TO BE PROCESSED, ANY BLANK LINE SHOULD
// GO AFTER THE REQUIRES BELOW.
//
//= require jquery
//= require base
//= require jquery_ujs
//= require twitter/bootstrap/alert
//= require twitter/bootstrap/transition
//= require twitter/bootstrap/collapse
//= require angular
//= require angular-ui-router
//= require angular-rails-templates
//= require lodash
//= require restangular
//= require angular-busy
//= require toastr
//= require angular-animate
//= require angularjs-toaster
//= require ng-table
//= require angular/app
//= require angular/controllers
//= require angular/services
//= require angular/services/crm
//= require angular/services/field_mapping
//= require angular/services/notification
//= require angular/controllers/crm
//= require angular/controllers/field_mappings
//= require angular/controllers/main
//= require angular/controllers/registration
//= require client
//= require video
//
//= require_tree ./templates
