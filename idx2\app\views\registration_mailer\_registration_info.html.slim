.step
  h3 Website information
  .name-value-pairs
    dl
      dt.label Theme
      dd.label= nbsp(registration.theme)
    dl
      dt.label Color scheme
      dd.label= nbsp(registration.color_scheme)
    dl
      dt.label Existing website
      dd.label= nbsp(registration.existing_website_url)
    dl
      dt.label Facebook URL
      dd.label= nbsp(registration.facebook_url)
    dl
      dt.label LinkedIn URL
      dd.label= nbsp(registration.linkedin_url)
    dl
      dt.label Twitter URL
      dd.label= nbsp(registration.twitter_url)
    dl
      dt.label Google Plus URL
      dd.label= nbsp(registration.google_plus_url)
    dl
      dt.label YouTube URL
      dd.label= nbsp(registration.youtube_url)
    dl
      dt.label Other social media
      dd.label= nbsp(registration.other_social_media)
    dl
      dt.label Logo image
      dd.label
        - if has_attachment?(:logo)
          span.value-image= image_tag attachment_url(:logo)
        - else
          span.value-empty= "[None entered]"
    dl
      dt.label Headshot image
      dd.label
        - if has_attachment?(:headshot)
          span.value-image= image_tag attachment_url(:headshot)
        - else
          span.value-empty= "[None entered]"

    dl
      dt.label Market area
      dd.label= nbsp(registration.market_area)
    dl
      dt.label Title
      dd.label= nbsp(registration.title)
    dl
      dt.label Tagline
      dd.label= nbsp(registration.tagline)
.step
  h3 Other info
  .name-value-pairs
    = render 'promo_code', promo_code: registration.promo_code
    dl
      dt.label Other notes
      dd.label= nbsp(registration.notes)
