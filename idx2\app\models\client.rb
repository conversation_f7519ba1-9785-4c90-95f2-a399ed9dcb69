class Client < ActiveRecord::Base
  self.table_name = "access"
  has_many :users, through: :user_client
  has_one :access_meta, dependent: :delete, foreign_key: "access_id"
  has_one :registration, dependent: :delete, foreign_key: "access_id"
  has_many :user_client, foreign_key: "access_id"
  has_many :videos, dependent: :delete_all, foreign_key: 'access_id'
  belongs_to :mls_system, foreign_key: "mls", primary_key: "name"
  belongs_to :reseller
  has_many :client_crms, dependent: :delete_all, foreign_key: "access_id", inverse_of: :client

  accepts_nested_attributes_for :registration, :access_meta

  default_scope { order('access_company') }

  validates :access_account_id, presence: true
  validates :access_apikey, presence: true
  validates :mls, presence: { message: "please select" }
  validates :access_emailaddress, email_format: true, presence: true, if: :from_registration
  validates :access_fullname, presence: true, unless: :access_account_id

  attr_accessor :from_registration

  before_validation :generate_values

  alias_attribute :name, :access_fullname
  alias_attribute :email, :access_emailaddress
  alias_attribute :company, :access_company
  alias_attribute :phone, :access_phone
  alias_attribute :address, :access_address
  alias_attribute :address2, :access_address2
  alias_attribute :zipcode, :access_zipcode
  # Having a field named account_id might hose us from having an association
  # named account. We shouldn't need such a table, but if we create one, we'll
  # likely need to rename this alias because of how Rails handles associations.
  alias_attribute :account_id, :access_account_id
  alias_attribute :apikey, :access_apikey

  def title
    self.company.presence || self.name
  end

  before_create :set_default_values
  # URLs is a TEXT field so it requires a non-null value.
  def set_default_values
    self.URLs = "" if self.URLs == nil
  end

  def ensure_account_id
    if self.access_account_id.blank? && !self.access_fullname.blank?
      # Change all to lowercase, remove non alpha-numeric characters.
      self.access_account_id = self.access_fullname.downcase.gsub(/[^0-9a-z]/i, '')
    end
  end

  # This can help us when constructing models that are for registration purposes.
  def self.default_registration_params
    { from_registration: true }
  end

  def get_urls
    self.URLs.split "\n"
  end

  # This is of course imperfect
  def first_name
    name.split(" ").first
  end

  # This is of course imperfect
  def last_name
    name.split(" ").drop(1).join
  end

  def address_lines
    "#{address} #{address2}"
  end

  def urls_split
    self.URLs.split("\n")
  end

  def get_crm_apis
    crms = []
    self.client_crms.where(enabled: true).each do |client_crm|
      crms << client_crm.inflate_api
    end
    crms
  end

  def self.register_idx_access_only(client)
    begin
      Client.transaction do
        if client.mls.present?
          access_meta = AccessMeta.build_from_registration(client)
        else
          msg = "Specify MLS"
          client.errors[:mls] << msg
          raise msg
        end

        client.save!
      end
    rescue Exception => ex
      # d ex.inspect.vh
      logger.info ex.inspect.vh
      logger.info ex.backtrace.join("\n\t")
      client.errors[:base] << ex.message
    else
      RegistrationMailer.delay.welcome_idx_access_only client

      # Notify the reseller and our admins
      # We must dup the array, or we might modify the actual Rails.configuration array.
      email_addresses = Rails.configuration.admin_email_addresses.dup
      email_addresses << client.reseller.admin_email if client.reseller
      email_addresses.each do |email_address|
        RegistrationMailer.delay.notify_reseller_idx_access_only client, email_address
      end
    end

    client
  end

  private
    def generate_values
      ensure_account_id
      if self.access_apikey.blank?
        # Generate a 15 character code.
        # See: http://stackoverflow.com/questions/34490/how-do-i-create-a-sha1-hash-in-ruby
        str = Digest::SHA2.base64digest(rand().to_s).to_s[0,15]
        self.access_apikey = str
      end
    end
end
