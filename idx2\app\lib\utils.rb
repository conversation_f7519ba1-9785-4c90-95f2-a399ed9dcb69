class Object
  def v(s)
    p s.vh
  end

  # vh is visibility helper
  # It helps us more easily see our debug lines.
  def vh(s = "", c: 60, inspect: true)
    s = "*" unless s.present?
    self_as_string = inspect ? self.inspect : self.to_s
    "#{s * c} #{self_as_string}"
  end
end

# For posterity, not necessarily semantically correct (module vs. class)
# my_redefinition_list = [Module, Class]
# my_redefinition_list.each do |object|
#   object.class_eval do
#     def self.class_exists?(class_name)
#       klass = Module.const_get(class_name)
#       return klass.is_a?(Class)
#     rescue NameError, LoadError
#       return false
#     end
#   end
# end

class Class
  def self.class_exists?(class_name)
    klass = Module.const_get(class_name)
    return klass.is_a?(Class)
  rescue NameError, LoadError
    return false
  end
end
