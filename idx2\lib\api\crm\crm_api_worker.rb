module Api
  module Crm
    class CrmApiWorker
      include Sidekiq::Worker
      sidekiq_options :retry => 0

      def perform(crm_api_class, serialization_data, metadata, data)
        crm_api = crm_api_class.constantize.inflate serialization_data
        logger.info('CrmApiWorker') { "About to have crm: #{crm_api} handle event: #{metadata['event_name']} with metadata: #{metadata} and data: #{data}" }
        crm_api.handle_event(metadata, data)
      end
    end
  end
end
