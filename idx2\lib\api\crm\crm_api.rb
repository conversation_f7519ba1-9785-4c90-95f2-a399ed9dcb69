module Api
  module Crm
  	class CrmApi
      class_attribute :display_name
      attr_accessor :apikey
      protected :apikey

      def initialize(apikey: "")
        self.apikey = apikey
      end

      def self.dispatch_for_all_crms(client, metadata, data)
        crms = client.get_crm_apis
        crms.select { |crm| crm.handles_event? metadata, data }.each do |crm|
          if crm.meant_for_job_queue?(metadata, data)
            crm.move_to_job_queue(metadata, data)
          else
            crm.handle_event(metadata, data)
          end
        end
      end

      def handles_event?(metadata, data)
        false
      end

      def handle_event(metadata, data)

      end

      def meant_for_job_queue?(metadata, data)
        false
      end

      def move_to_job_queue(metadata, data)
        CrmApiWorker.perform_async(self.class.name, serialize_data, metadata, data)
      end

      def serialize_data
        { apikey: apikey }
      end

      def self.available_apis
        [
          WiseAgent::WiseAgent,
          LionDesk::LionDesk
        ]
      end

      def self.inflate(serialization_data)
        # If serialization_data is coming from redis, the kash keys will be
        # strings, not symbols. We need to fix that.
        serialization_data = serialization_data.symbolize_keys if serialization_data.first[0].kind_of?(String)
        self.new **serialization_data
      end
  	end
  end
end
