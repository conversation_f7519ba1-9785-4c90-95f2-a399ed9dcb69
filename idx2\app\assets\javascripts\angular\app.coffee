app = angular.module('app', ['cgBusy', 'n', 'ui.router', 'templates', 'pfmlsControllers'])
app.config ['$stateProvider', '$urlRouterProvider', ($stateProvider, $urlRouterProvider) ->
  # Since we don't have a real SPA but instead a hybrid between traditional
  # rails site and SPA, but I want to have one single "app", I'll handle the
  # routing dynamically based on the real url.
  pathname = window.location.pathname
  if /^\/clients\/\d+$/.test pathname
    $stateProvider
    .state 'client',
      url: '/'
      views:
        crms:
          templateUrl: 'crms.html'
          controller: 'CrmsCtrl'
          controllerAs: 'cc'
  else
    $stateProvider
    .state 'field_mappings',
      url: '/field_mappings'
      templateUrl: 'field_mappings.html'
      controller: 'FieldMappingsCtrl'
      controllerAs: 'fm'
    .state 'main',
      url: '/'
      templateUrl: 'main.html'
      controller: 'MainCtrl'
      controllerAs: 'main'
  $urlRouterProvider.otherwise '/'
]
