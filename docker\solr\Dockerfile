# Use official Solr image as base
FROM solr:8.11.2

USER root

# Install additional packages needed
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    wget \
    procps \
    make \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js for tools
RUN curl -fsSL https://deb.nodesource.com/setup_16.x | bash - \
    && apt-get install -y nodejs

# Install IcedCoffeeScript and CoffeeScript
RUN npm install -g iced-coffee-script coffee-script

# Set working directory
WORKDIR /opt/solr

# Declare a volume for cores
VOLUME /var/solr

# Switch back to solr user
USER solr

# Command for running Solr
CMD ["solr-foreground"]
