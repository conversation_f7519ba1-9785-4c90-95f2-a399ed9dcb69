module = angular.module 'pfmlsControllers'
module.controller "regCtrl", ['$scope', 'Restangular', 'notification', ($scope, Restangular, notification) ->
  $scope.refresh = ->
    q = Restangular.one 'registrations', $scope.registrationId
    p = q.get()
    $scope.myPromise = p
    success = (registration) ->
      $scope.status = registration.website_state
      notification.success "Updated website status"
    fail = (obj) ->
      msg = obj.data.message
      notification.error msg, "Failed to refresh status"
    p.then(success).catch(fail)
  $scope.build = ->
    q = Restangular.one 'registrations', $scope.registrationId
    p = q.post('build')
    $scope.myPromise = p
    success = (registration) ->
      notification.success "Kicked off website build"
    fail = (obj) ->
      msg = obj.data.message
      notification.error msg, "Failed to kick off website build"
    p.then(success).catch(fail)
  $scope.reset = ->
    q = Restangular.one 'registrations', $scope.registrationId
    p = q.post('reset')
    $scope.myPromise = p
    success = (registration) ->
      notification.success "Reset website state"
    fail = (obj) ->
      msg = obj.data.message
      notification.error msg, "Failed to reset website state"
    p.then(success).catch(fail)
]
