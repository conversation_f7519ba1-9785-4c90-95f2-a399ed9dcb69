module SignIn
  def get_user_properties(name)
    user_hash = {}
    case name
      when 'admin'
        user_hash[:email] = "<EMAIL>"
        user_hash[:password] = "iamadmin"
        user_hash[:admin] = true
      when 'client'
        user_hash[:email] = "<EMAIL>"
        user_hash[:password] = "iamclient"
      else
        user_hash[:email] = "#{name}@example.com"
        user_hash[:password] = "password"
    end
    user_hash
  end

  def create_user(name)
    user_hash = get_user_properties(name)
    user = FactoryGirl.create(:user, user_hash)
  end
  
  def sign_in_as(name)
    user_hash = get_user_properties(name)
    user = create_user(name)
    sign_in_with_credentials(user_hash)
  end

  def sign_in_with_credentials(credentials_hash)
    visit user_session_url
    fill_in "user_email", with: credentials_hash[:email]
    fill_in "user_password", with: credentials_hash[:password]
    click_button "Sign in"
  end
end

World(SignIn)

Given(/^I(?:'| a)m not logged in$/) do
  # Don't need to do anything. We could optionally ensure the user is logged out.
end

Given(/^I(?:'| a)m logged in with: email: "?([^"]+)"?, password: "?([^"]+)"?$/) do |email, password|
  credentials = {
    email: email,
    password: password
  }
  sign_in_with_credentials credentials
end

Given(/^I(?: a|')m logged in as "?(.+)"?$/) do |name|
  sign_in_as name
end
