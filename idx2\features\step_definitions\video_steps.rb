Given(/^I have two clients and their associated users, each with a video$/) do
  user1 = create_user "client1"
  user2 = create_user "client2"
  client1 = FactoryGirl.create(:client, access_company: "client1")
  client2 = FactoryGirl.create(:client, access_company: "client2")
  user_client1 = FactoryGirl.create(:user_client, user: user1, client: client1)
  user_client2 = FactoryGirl.create(:user_client, user: user2, client: client2)
  FactoryGirl.create(:video, client: client1)
  FactoryGirl.create(:video, client: client2)
end