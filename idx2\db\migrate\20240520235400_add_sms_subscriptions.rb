class AddSmsSubscriptions < ActiveRecord::Migration
  def change
    create_table :sms_subscriptions do |t|
      t.string :from
      t.string :to
      t.string :status

      t.timestamps
    end

    add_index :sms_subscriptions, :from
    add_index :sms_subscriptions, :to

    create_table :sms_subscription_uses do |t|
      t.integer :sms_subscriptions_id
      t.integer :access_id
      t.string :site_url

      t.timestamps
    end

    add_index :sms_subscription_uses, :sms_subscriptions_id
    add_index :sms_subscription_uses, :access_id
  end
end
