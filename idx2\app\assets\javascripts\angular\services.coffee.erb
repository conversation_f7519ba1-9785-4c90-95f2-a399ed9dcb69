module = angular.module 'pfmlsServices', ['restangular']
module.config ['RestangularProvider', (RestangularProvider) ->
  RestangularProvider.setRequestSuffix '.json'
  hostname = window.location.hostname
  hostname = "api.ifoundagent.com" if hostname == "admin.ifoundagent.com"
  hostname = "api.domain.com" if hostname == "admin.domain.com"
  RestangularProvider.setBaseUrl "//#{hostname}:#{window.location.port}"
  RestangularProvider.setDefaultHttpFields
    withCredentials: true
]
