class ClientsController < ApplicationController
  special_pages = [:new, :create, :new_idx_access_only, :create_idx_access_only]
  before_filter :authenticate_user!, except: special_pages
  prepend_before_filter :allow_iframe_requests, only: special_pages
  before_filter :prepare_whitelabel, only: special_pages
  before_filter :prepare_customizations, only: special_pages
  helper_method :get_themes

  # force_ssl_with_configured_port only: [:new, :create, :update, :destroy, :edit]

  def index
    authorize! :manage, Client
    @q = Client.search(params[:q])
    @clients = @q.result.includes(:reseller)
  end

  def new_setup
    @title = "New Client Sign Up"

    if current_user
      flash.now[:warning] = "You may not be signed in to sign up as a new client. Please sign out first. If you are seeing this from within an iframe, you'll need to visit the main iFoundAgent admin site."
      render text: '', layout: true and return
    end

    @client = Client.new Client.default_registration_params
  end

  def new
    new_setup

    # Reminder on `performed?`: http://blog.arkency.com/2014/07/4-ways-to-early-return-from-a-rails-controller/
    return if performed?
    @registration = @client.build_registration
  end

  def new_idx_access_only
    new_setup
    return if performed?
    @form_action_url = create_idx_access_only_clients_path
    render 'new'
  end

  def show
    @client = Client.find(params[:id])

    @title = @client.title

    @registration = @client.registration
    @access_meta = @client.access_meta
  end

  def edit
    @client = Client.find(params[:id])
  end

  def update
    @client = Client.find(params[:id])
    respond_to do |format|
      if @client.update(update_params)
        format.html { redirect_to @client, notice: "The data was saved successfully" }
      else
        flash.now[:alert] = "There were some errors during update. #{@client.errors.first}"
        format.html { render :edit }
      end
    end
  end

  def create
    @title = "New Client Sign Up"
    @client = Client.new client_params.merge(Client.default_registration_params)

    respond_to do |format|
      reg_params = params.fetch(:client).slice(:registration)
      Registration.register(@client, reg_params) do |client, registration|
        @client = client
        @registration = registration
      end
      if @client.persisted?
        query_params = {}
        if @client.reseller_id
          @client.reseller_id.vh
          reseller_friendlyid = Reseller.find(@client.reseller_id).friendlyid
          query_params[:reseller_friendlyid] = reseller_friendlyid
          query_params[:promo_code] = @registration.promo_code
          query_params[:theme] = current_theme
        end

        format.html { redirect_to registration_success_path(query_params), notice: "Registration successful!" }
      else
        format.html { render action: 'new' }
      end
    end
  end

  def create_idx_access_only
    @title = "New Client Sign Up"
    @form_action_url = create_idx_access_only_clients_path
    @client = Client.new client_params.merge(Client.default_registration_params)
    respond_to do |format|
      @client = Client.register_idx_access_only(@client)
      if @client.persisted?
        reseller_friendlyid = ''
        if @client.reseller_id
          reseller_friendlyid = Reseller.find(@client.reseller_id).friendlyid
        end
        query_params = { reseller_friendlyid: reseller_friendlyid }
        format.html { redirect_to registration_success_idx_access_only_path(query_params), notice: "Registration successful!"}
      else
        format.html { render action: 'new' }
      end
    end
  end

  def destroy
    @client = Client.find(params[:id])

    @client.destroy

    respond_to do |format|
      format.html { redirect_to clients_path }
    end
  end

  def seo
    @client = Client.find(params[:client_id])
    mls_system = MlsSystem.friendly.find(@client.mls)
    mls_system.copy_from_client(@client)
    redirect_to @client, notice: "SEO values copied to MLS System as defaults"
  end

  def registration
    @client = Client.find params[:client_id]
    @registration = @client.registration

    name = @client.title
    @title = "Registration: #{name}"
  end

  def get_themes
    Registration.get_themes(@reseller)
  end

  def prepare_customizations
    # Hack for customizing the form for WestUSA
    @is_westusa = current_reseller.try(:friendlyid) == "westusa01"
    @is_main_mu = current_reseller.try(:friendlyid).nil? or /getfoundphx/.match(current_reseller.try(:website_server))
  end

  private
    def client_params
      params.require(:client).permit(
        :access_member_id,
        :access_office_id,
        :access_youtube_id,
        :domain,
        :ftp_addr,
        :ftp_user,
        :ftp_pass,
        :mls,
        :access_domain,
        :access_ip,
        :access_company,
        :access_fullname,
        :access_address,
        :access_address2,
        :access_city,
        :access_state,
        :access_zipcode,
        :access_phone,
        :access_emailaddress,
        :access_status,
        :hide_ucb,
        :URLs,
        :reseller_id,
        :broker_office_name,
        :feature_gui
      )
    end

    def update_params
      params.require(:client).permit(
        :access_account_id,
        :access_apikey,
        :access_member_id,
        :access_office_id,
        :access_youtube_id,
        :domain,
        :ftp_addr,
        :ftp_user,
        :ftp_pass,
        :mls,
        :access_domain,
        :access_ip,
        :access_company,
        :access_fullname,
        :access_address,
        :access_address2,
        :access_city,
        :access_state,
        :access_zipcode,
        :access_phone,
        :access_emailaddress,
        :access_status,
        :hide_ucb,
        :URLs,
        :feature_gui,
        :reseller_id,
        :broker_office_name,
        # We must allow the id fields to be updated, or new records will be created.
        registration_attributes: Registration.params_list | [:id],
        access_meta_attributes: AccessMeta.params_list | [:id],
      )
    end
end
