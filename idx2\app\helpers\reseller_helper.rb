module ResellerHelper
  def signup_page_code(reseller_friendlyid)
    url = new_idx_access_only_clients_url(reseller_friendlyid: reseller_friendlyid)
    <<END
<!-- The following javascript will listen to the iframe for an event, telling to scroll to the top of the page when the iframe form is submitted -->
<script>// <![CDATA[
jQuery(function() {jQuery('.get-started-form').load(function() {var eventMethod = window.addEventListener ? "addEventListener" : "attachEvent"; var eventer = window[eventMethod]; var messageEvent = eventMethod == "attachEvent" ? "onmessage" : "message"; eventer(messageEvent,function(e) {if (e.data == 'pfmls-get-started-form-submit') { jQuery('html, body').animate({ scrollTop: jQuery('iframe').offset().top - 100 }, 'fast');  } },false);}); });
// ]]></script>
<iframe class="get-started-form" src="#{url}" width="900px" height="1200px" frameborder="0"></iframe>
END
  end

  def pay_page_code(reseller_friendlyid)
    url = pay_url(reseller_friendlyid: reseller_friendlyid, promo_code: 'PROMO_CODE_HERE')
    <<END
<iframe class="get-started-form" src="#{url}" width="1050px" height="600px" frameborder="0"></iframe>
END
  end
end
