/*
  Reminder: @import doesn't seem to work here for emails.
  Update: I haven't tried this yet but found this comment on the ruby-stylus
  github page: "If you use <PERSON><PERSON><PERSON> @import to expose variables, mixins or just to concatenate code, you should use only the .styl extension on your imported files. If you use the .css.styl form (a convention from Sprockets), <PERSON><PERSON><PERSON> will treat it as a plain CSS file since it has .css on its name."
*/
#outlook a{
  padding:0;
}
body{
  width:100% !important;
}
.ReadMsgBody{
  width:100%;
}
.ExternalClass{
  width:100%;
}
body{
  -webkit-text-size-adjust:none;
}
body{
  margin:0;
  padding:0;
}
img{
  border:0;
  height:auto;
  line-height:100%;
  outline:none;
  text-decoration:none;
}
table td{
  border-collapse:collapse;
}
#backgroundTable{
  height:100% !important;
  margin:0;
  padding:0;
  width:100% !important;
}
body,#backgroundTable{
  background-color:#FAFAFA;
}
#templateContainer{
  border:1px solid #DDDDDD;
}
h1,.h1{
  color:#202020;
  display:block;
  font-family:Arial;
  font-size:34px;
  font-weight:bold;
  line-height:100%;
  margin-top:0;
  margin-right:0;
  margin-bottom:10px;
  margin-left:0;
  text-align:left;
}
h2,.h2{
  color:#202020;
  display:block;
  font-family:Arial;
  font-size:30px;
  font-weight:bold;
  line-height:100%;
  margin-top:0;
  margin-right:0;
  margin-bottom:10px;
  margin-left:0;
  text-align:left;
}
h3,.h3{
  color:#202020;
  display:block;
  font-family:Arial;
  font-size:26px;
  font-weight:bold;
  line-height:100%;
  margin-top:0;
  margin-right:0;
  margin-bottom:10px;
  margin-left:0;
  text-align:left;
}
h4,.h4{
  color:#202020;
  display:block;
  font-family:Arial;
  font-size:22px;
  font-weight:bold;
  line-height:100%;
  margin-top:0;
  margin-right:0;
  margin-bottom:10px;
  margin-left:0;
  text-align:left;
}
#templatePreheader{
  background-color:#FAFAFA;
}
.preheaderContent div{
  color:#505050;
  font-family:Arial;
  font-size:10px;
  line-height:100%;
  text-align:left;
}
.preheaderContent div a:link,.preheaderContent div a:visited,.preheaderContent div a .yshortcuts {
  color:#336699;
  font-weight:normal;
  text-decoration:underline;
}
#templateHeader{
  background-color:#FFFFFF;
  border-bottom:0;
}
.headerContent{
  color:#202020;
  font-family:Arial;
  font-size:34px;
  font-weight:bold;
  line-height:100%;
  padding:0;
  text-align:center;
  vertical-align:middle;
}
.headerContent a:link,.headerContent a:visited,.headerContent a .yshortcuts {
  color:#336699;
  font-weight:normal;
  text-decoration:underline;
}
#headerImage{
  height:auto;
  max-width:600px;
}
#templateContainer,.bodyContent{
  background-color:#FFFFFF;
}
.bodyContent div{
  color:#505050;
  font-family:Arial;
  font-size:14px;
  line-height:150%;
  text-align:left;
}
.bodyContent div a:link,.bodyContent div a:visited,.bodyContent div a .yshortcuts {
  color:#336699;
  font-weight:normal;
  text-decoration:underline;
}
.bodyContent img{
  display:inline;
  height:auto;
}
#templateFooter{
  background-color:#FFFFFF;
  border-top:0;
}
.footerContent div{
  color:#707070;
  font-family:Arial;
  font-size:12px;
  line-height:125%;
  text-align:left;
}
.footerContent div a:link,.footerContent div a:visited,.footerContent div a .yshortcuts {
  color:#336699;
  font-weight:normal;
  text-decoration:underline;
}
.footerContent img{
  display:inline;
}
#social{
  background-color:#FAFAFA;
  border:0;
}
#social div{
  text-align:center;
}
#utility{
  background-color:#FFFFFF;
  border:0;
}
#utility div{
  text-align:center;
}
#monkeyRewards img{
  max-width:190px;
}

td
  color #505050
  font-family Arial
  font-size 14px
