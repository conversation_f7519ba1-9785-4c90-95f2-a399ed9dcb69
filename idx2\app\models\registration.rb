class Registration < ActiveRecord::Base
  belongs_to :client, foreign_key: "access_id"

  validates :theme, presence: { message: "please select" }
  validates :color_scheme, presence: { message: "please select" }

  before_create :set_default_values

  class_attribute :default_themes
  Registration.default_themes = ["Marketing Platform Only", "Prime", "Summit", "Jinger", "Pinnacle", "Denali", "Mont Blanc", "Rainier"]


  def set_default_values
    self.promo_code = "LATER" unless self.promo_code.present?
  end

  mount_uploader :headshot, ImageUploader
  mount_uploader :logo, ImageUploader

  state_machine :website_state, initial: :not_started, namespace: :website do
    state :error
    state :none
    event :progress do
      transition not_started: :building
      transition error: :building
      transition building: :done
    end

    after_transition to: :building do |registration, transition|
      password = transition.args.first[:password]
      registration.build_website password
    end
  end

  state_machine :payment_state, initial: :need_to_pay do
    state :none
    state :paid
    event :mark_paid do
      transition need_to_pay: :paid
    end
    after_transition from: :need_to_pay, to: :paid do |registration, transition|
      registration.create_recurring_billing
    end
  end

  def build_website(password)
    NewWebsite.new.build self, password
  end

  def compute_setup_payment
    case self.promo_code.try(:upcase)
    when "LATER"
      0
    when "PRIME"
      999
    else
      499
    end
  end

  def create_recurring_billing
    logger.info "REMINDER!!!!!!!! Need to create recurring billing here!"
  end

  def handle_promo_code_by_theme(client, registration)
    reseller = client.reseller
    if reseller
      reseller_pricing = reseller.reseller_pricings.where(theme: registration.theme).first
      client.registration.promo_code = reseller_pricing.promo_code if reseller_pricing
    end
  end

  # This is meant to be called from our controller. It will save the client
  # record and also create other needed records such as access_meta and user.
  # We take an implicit block to return any registration errors.
  def self.register(client, reg_params)
    registration = nil
    user = nil
    access_meta = nil
    password = nil

    begin
      Client.transaction do
        registration = client.build_registration(registration_params(reg_params))
        registration.handle_promo_code_by_theme client, registration
        if client.mls.present?
          access_meta = AccessMeta.build_from_registration(client)
        else
          msg = "Specify MLS"
          client.errors[:mls] << msg
          raise
        end

        client.save!

        # If there's already a user with this email address, we don't create a new
        # user but instead link the existing user and new client.
        user = User.where(email: client.access_emailaddress).first
        if user
          user.clients << client
          # Reminder: I have a test that says it's possible to create multiple registrations
          # with the same email address, but I've since added payment stuff that always grabs
          # the user's first client record. So, when we come to the day where we really want to
          # allow it, that's what we'll have to go change.
          raise "That email address has already been used. Multiple registrations not yet supported."
        else
          user = User.build_from_registration(client)
          password = user.password
        end
        user.save!

        # Strangeness. In production, the user_client record is not created. I
        # can't explain why. So we force its creation here.
        user_client = UserClient.find_or_create_by(access_id: client.id, user_id: user.id)

        # For debugging
        #raise ActiveRecord::Rollback.new("Test error message")
        # client.errors.add(:access_state, "My test error message")
        # d client.errors.full_messages
      end
    rescue Exception => ex
      # d ex.inspect.vh
      logger.info ex.inspect.vh
      logger.info ex.backtrace.join("\n\t")
      client.errors[:base] << ex.message
    else
      RegistrationMailer.delay.welcome(user, password, client, registration)

      # Notify the reseller and our admins
      # We must dup the array, or we might modify the actual Rails.configuration array.
      email_addresses = Rails.configuration.admin_email_addresses.dup
      email_addresses << client.reseller.admin_email if client.reseller
      email_addresses.each do |email_address|
        RegistrationMailer.delay.notify_admin(client, registration, email_address)
      end
      NewWebsiteWorker.perform_async(registration.id, password)
    end

    yield client, registration, access_meta if block_given?
  end

  def self.get_packages
    packages = {}
    Package.get_packages.each { |p| packages[p.description] = p.promo_code }
    packages
  end

  def self.get_themes(reseller)
    themes = self.get_theme_to_color_schemes_map.keys.sort
    if reseller.nil?
    # main MU
      themes = themes.select { |key, value|
        Registration.default_themes.include? key
      }
    elsif /getfoundsd.com/.match(reseller.website_server)
      themes = themes.select { |key, value|
        ["Prime", "Summit", "Jinger"].include? key
      }
    else
      case reseller.friendlyid
      when "westusa01"
        themes = themes.select { |key, value|
          ["Phoenix (ARMLS)", "Payson (CABOR)", "White Mountains (WMAR)"].include? key
        }
      else
        # default for new MUs.
        themes = themes.select { |key, value|
          Registration.default_themes.include? key
        }
      end
    end

    themes
  end

  def self.get_mapped_color_schemes(theme)
    self.get_theme_to_color_schemes_map()[theme].sort
  end

  def self.get_theme_to_color_schemes_map
    {
      "8" => %W(Blue Silver Wood #{"Dark Wood"} Red Gray),
      "84" => %W(Blue Silver Wood #{"Dark Wood"} Red Gray),
      "Eleven43" => %W(Gray Black Orange Blue White),
      "Ivy" => %W(Black Blue Wood Red),
      "Jinger" => %W(Default Black Blue Corporate Orange White),
      "Jinger Video" => %W(Default Black Blue Corporate Orange White),
      "Prime" => %W(Charcoal Orange Blue Red Black White),
      "Rysky" => %W(Blue Silver Wood #{"Dark Wood"} Red Gray White Black),
      "Rysky Pro" => %W(Blue Silver Wood #{"Dark Wood"} Red Gray White Black),
      "Rysky Too" => %W(Blue Silver Wood #{"Dark Wood"} Red Gray White Black),
      "Sky" => %W(Silver Wood Red),
      "Summit" => %W(Default Black Blue Corporate Orange White),
      "Summit V" => %W(Default Black Blue Corporate Orange White),
      "Pinnacle" => %W(Default Black Blue Carbon Orange White),
      "Phoenix (ARMLS)" => %W(Default Black Blue White),
      "Payson (CABOR)" => %W(Default Black Blue White),
      "White Mountains (WMAR)" => %W(Default Black Blue White),
      "Marketing Platform Only" => %W(Default Black Blue Orange White),
      "Denali" => %W(Default),
      "Mont Blanc" => %W(Default),
      "Rainier" => %W(Default),
    }
  end

  def self.params_list
    [
      :theme,
      :color_scheme,
      :market_area,
      :existing_website_url,
      :facebook_url,
      :linkedin_url,
      :twitter_url,
      :google_plus_url,
      :youtube_url,
      :other_social_media,
      :notes,
      :logo,
      :logo_cache,
      :remove_logo,
      :headshot,
      :headshot_cache,
      :remove_headshot,
      :tagline,
      :title,
      :promo_code,
      :epn_response,
      :website_state,
      :website_request,
      :website_response,
      :payment_state,
    ]
  end

private
  def self.registration_params(reg_params)
    reg_params.require(:registration).permit self.params_list
  end
end
