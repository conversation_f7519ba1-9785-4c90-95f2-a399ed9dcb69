showInfo = (tr) ->
  video_id = tr.data("youtube-id")
  # TODO: We could show MLS listing info here, but I'll wait for any feedback asking for it.
  tr.find(".video_placeholder").html "<iframe width='420' height='315' src='http://www.youtube.com/embed/#{video_id}' frameborder='0' allowfullscreen></iframe>"

hideInfo = (tr) ->
  tr.find(".video_placeholder").html("")

jQuery ->
  $(".videos").on "click", "a.show-info", (event) ->
  	event.preventDefault()
  	#alert "click!"
  	el = $(event.target)
  	console.dir el
  	if el.hasClass "showing-info"
      hideInfo el.parent().parent()
      el.removeClass "showing-info"
      el.html "<i class='icon-info-sign icon-white'></i> Show more info"
    else
      showInfo el.parent().parent()
      el.addClass "showing-info"
      el.html "<i class='icon-info-sign icon-white'></i> Hide info"
