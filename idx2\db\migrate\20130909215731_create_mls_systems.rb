class CreateMlsSystems < ActiveRecord::Migration
  def change
    create_table :mls_systems do |t|
      t.string :name
      t.string :display_name
      t.string :meta_prop_url
      t.string :meta_prop_title
      t.string :meta_prop_h1
      t.text   :meta_prop_keywords
      t.text   :meta_prop_description
      t.string :meta_result_title
      t.string :meta_result_h1
      t.text   :meta_result_keywords
      t.text   :meta_result_description
      t.string :meta_result_prop_h2
      t.text   :meta_result_prop_content
      t.string :meta_cat_title
      t.string :meta_cat_h1
      t.text   :meta_cat_keywords
      t.text   :meta_cat_description
      t.string :meta_geoapi
      t.text   :meta_links_seo, limit: 16777215
    end
  end
end
