div ui-view="crms" ng-init="clientId=#{@client.id}"
  strong Wise Agent
  p To add a Wise Agent CRM connection, you'll need the API key. To get it, use the following command line, replacing [USERNAME] with the Wise Agent username.
  pre curl -X POST -H "Cache-Control: no-cache" -H "Content-Type: application/x-www-form-urlencoded" -d 'requestType=userid&username=[USERNAME]' https://sync.thewiseagent.com/http/webconnect.asp
  p You'll get back an answer like
  pre 12,345,678,90,12,123,45,67,890
  p which you should use as the API key.
