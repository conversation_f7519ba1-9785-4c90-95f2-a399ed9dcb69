module GravityForms
  module Crm
    class WiseAgent
      def get_event_handler(metadata, data, wise_agent)
        if self.is_contact_form data
          Proc.new do
            request_params = self.get_contact_data data
            wise_agent.create_contact request_params
          end
        end
      end

      # To be considered a contact form, it must have first name, and
      # at least email or phone.
      def is_contact_form(data)
        entry = data['entry']
        form = data['form']
        form_processor = GravityForms::FormProcessor.new entry, form

        return false unless form_processor.first_by_type_then_label('name', 'First')
        return true if form_processor.first_by_type('email').present?
        form_processor.first_by_type('phone').present?
      end

      def get_contact_data(data)
        entry = data['entry']
        form = data['form']
        form_processor = GravityForms::FormProcessor.new entry, form

        message = self.build_message form, form_processor
        request_params = {
          CFirst: form_processor.first_by_type_then_label('name', 'First'),
          CLast: form_processor.first_by_type_then_label('name', 'Last'),
          CEmail: form_processor.first_by_type('email'),
          Phone: form_processor.first_by_type('phone'),
          Message: message
        }
      end

      # The current use of this is to get a multi-line string that shows the
      # form title and all the values aside from the contact info. To keep it
      # simple at first, I'll just include all fields (as opposed to trying to
      # cut out the contact fields).
      # Wise Agent doesn't format the notes when they output as HTML, so they
      # said we should.
      def build_message(form, form_processor)
        message = "Form: #{form.andand['title']}\n"
        message += form_processor.format_fields
        message.gsub("\n", "<br />\n")
      end
    end
  end
end
