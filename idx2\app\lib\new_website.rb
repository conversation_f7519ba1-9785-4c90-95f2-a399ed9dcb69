class NewWebsite
  include Logging

  def build(registration, password)
    response = NewWebsiteApi.new.build(registration, password) do |rq,rsp|
      registration.website_request = rq
      registration.save!
    end
    logger.info "New website build response was #{response}"
    if response.code == 200
      properly_parsed_json = false
      json = nil
      begin
        json = JSON.parse(response.body)
        properly_parsed_json = true
      rescue
        # Don't need to do anything.
      end
      if properly_parsed_json && json['success'] == true
        registration.progress_website
        domain = json['domain']
        url = "http://#{domain}"
        client = registration.client
        client.URLs += (client.URLs.present? ? "\n" : "") + url
        client.save!
      else
        registration.website_state = "error"
      end
      registration.website_response = response.body
      registration.save!
    else
      registration.website_state = "error"
      registration.website_response = ["#{response.code}: #{response.body}"]
      registration.save!
    end
    RegistrationMailer.delay.site_build(registration)
  end
end
