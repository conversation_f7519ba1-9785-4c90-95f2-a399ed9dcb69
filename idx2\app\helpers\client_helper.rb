module Client<PERSON>el<PERSON>
  def get_pfmls_idx_update_url(url)
    url = "http://#{url}" unless url.start_with?('http')
    url += "/?pfmls_idx_update"
  end

  def theme_previews_url
    current_reseller.try(:theme_previews_url) || "http://ifoundagent.com/portfolio"
  end

  def support_email
    if current_theme == "Free" and current_reseller.try(:support_email)
      current_reseller.try(:support_email)
    else
      "<EMAIL>"
    end
  end

  def sliders_url
    mainsite = "http://www.ifoundagent.com"
    current_reseller.try(:sliders_url) || "#{mainsite}/slider-images-xlarge/"
  end

  # Case 1417, special handling for Tierra Antigua.
  def get_modified_theme_to_color_schemes_map
    map = Registration.get_theme_to_color_schemes_map
    if request.referer && request.referer.include?("tierra-antigua-website-idx-signup/")
      ["<PERSON><PERSON><PERSON>", "R<PERSON>ky Too", "Rysky Pro", "<PERSON>", "Ivy", "8", "84"].each do |theme|
        map[theme] += ["Purple", "Turquoise", "Brown"]
      end
      map["Eleven43"] += ["Purple", "Turquoise", "Brown", "Tierra"]
      ["Prime", "Summit", "Summit V"].each do |theme|
        map[theme] += ["Tierra"]
      end
    end
    map
  end
end
