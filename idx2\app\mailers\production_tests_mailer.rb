class ProductionTestsMailer < ActionMailer::Base
  default from: Mail::AddressContainer.new("ProFound IDX <<EMAIL>>")

  def self.get_email_test_to
    Rails.configuration.try(:production_tests).try(:email_test).try(:to).try(:dup) || Mail::AddressContainer.new("<PERSON> <<EMAIL>>")
  end

  def self.get_email_test_from
    Rails.configuration.try(:production_tests).try(:email_test).try(:from).try(:dup) || default_params[:from]
  end

  def email_test(hash = {})
    from = self.class.get_email_test_from
    to = self.class.get_email_test_to
    attachments.inline['email_header.jpg'] = File.read(File.join(Rails.root, 'app/assets/images/ifoundagent_email_header.jpg'))
    @hash = hash
    mail from: from, to: to, subject: "Profound production email test"
  end
end
