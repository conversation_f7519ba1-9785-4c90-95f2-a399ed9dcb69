---
http_interactions:
- request:
    method: post
    uri: https://partnerapikey:@api-v1.liondesk.com/
    body:
      encoding: UTF-8
      string: '{"firstname":"<PERSON><PERSON>","lastname":"<PERSON><PERSON><PERSON><PERSON><PERSON>","email":"<EMAIL>","phone":"************","comments":"Form:
        Schedule to see this house\nMLS number: 1234567\nURL: http://www.example.com/property-idx-1234567\nMessage:
        My message here\n","action":"NewSubmission"}'
    headers:
      X-Liondesk-Id:
      - myuserkey
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json
      Server:
      - Microsoft-IIS/8.0
      X-Powered-By:
      - ASP.NET
      - PHP/5.5.3
      Date:
      - Fri, 11 Sep 2015 21:36:59 GMT
      Content-Length:
      - '41'
    body:
      encoding: UTF-8
      string: |2-



        {"error":0,"errorText":"","id":118263}
    http_version:
  recorded_at: Fri, 11 Sep 2015 21:37:15 GMT
recorded_with: VCR 2.9.1
